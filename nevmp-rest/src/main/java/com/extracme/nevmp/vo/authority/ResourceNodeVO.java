package com.extracme.nevmp.vo.authority;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/8/28
 */
@Data
public class ResourceNodeVO {

    /**
     * 节点ID
     */
    @NotNull
    @NotBlank
    private String id;

    /**
     * 名称
     */
    @NotNull
    @NotBlank
    private String name;

    /**
     * 接口
     */
    private String url;

    /**
     * 描述
     */
    private String description;

    /**
     * 父节点Id
     */
    private String syresourceId;


    /**
     * 0 菜单类型会显示在系统首页左侧菜单中
     * 1 功能类型不会显示在系统首页左侧菜单中
     */
    private String resourceType;
}
