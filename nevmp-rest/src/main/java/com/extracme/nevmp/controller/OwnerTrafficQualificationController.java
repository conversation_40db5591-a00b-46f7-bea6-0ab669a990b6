package com.extracme.nevmp.controller;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;
import org.springframework.web.multipart.MultipartFile;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.RequestLock;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.qualification.traffic.CancelTrafficQualificationDTO;
import com.extracme.nevmp.dto.qualification.traffic.OwnerTrafficQualificationDTO;
import com.extracme.nevmp.dto.qualification.traffic.SearchOwnerTrafficQualificationDTO;
import com.extracme.nevmp.dto.qualification.traffic.TrafficExportByte;
import com.extracme.nevmp.dto.qualification.traffic.UploadOwnerTrafficQualificationDTO;
import com.extracme.nevmp.service.OwnerTrafficQualificationService;
import com.extracme.nevmp.vo.CancelTrafficQualificationVO;
import com.extracme.nevmp.vo.SearchOwnerTrafficQualificationVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 */
@Authorize
@RestController
@RequestMapping("ownerTrafficQualification")
@Validated
@Api(value = "公安用户购车资质管理")
@Slf4j
public class OwnerTrafficQualificationController {


    @Autowired
    private OwnerTrafficQualificationService ownerTrafficQualificationService;


    @PostMapping("/searchOwnerTrafficQualification")
    @ApiOperation(value = "查询公安意向用户列表", httpMethod = "POST")
    public PageInfoBO<OwnerTrafficQualificationDTO> searchOwnerTrafficQualification(@RequestBody @Validated SearchOwnerTrafficQualificationVO searchOwnerTrafficQualificationVO) {
        if (searchOwnerTrafficQualificationVO.getApplyEndTime() != null) {
            searchOwnerTrafficQualificationVO.setApplyEndTime(new Date(searchOwnerTrafficQualificationVO.getApplyEndTime().getTime() + 24 * 60 * 60 * 1000L));
        }
        searchOwnerTrafficQualificationVO.setDriverLicenseType(Boolean.TRUE.equals(searchOwnerTrafficQualificationVO.getIsShangHaiLicense()) ? 0 : 1);
        SearchOwnerTrafficQualificationDTO searchOwnerTrafficQualificationDTO = ConvertUtil.convert(searchOwnerTrafficQualificationVO, SearchOwnerTrafficQualificationDTO.class);
        PageInfoBO<OwnerTrafficQualificationDTO> pageInfo = ownerTrafficQualificationService.searchOwnerTrafficQualification(searchOwnerTrafficQualificationDTO);
        return pageInfo;
    }

    /**
     * 导出意向用户交通审核列表
     */
    @RequestLock
    @PostMapping("/exportTrafficQualificationReview")
    @ApiOperation(value = "导出意向用户交通审核列表", httpMethod = "POST")
    public ResponseEntity<byte[]> exportTrafficQualificationReview(@RequestBody SearchOwnerTrafficQualificationVO searchOwnerTrafficQualificationVO) {
        if (searchOwnerTrafficQualificationVO.getApplyEndTime() != null) {
            searchOwnerTrafficQualificationVO.setApplyEndTime(new Date(searchOwnerTrafficQualificationVO.getApplyEndTime().getTime() + 24 * 60 * 60 * 1000L));
        }

        searchOwnerTrafficQualificationVO.setDriverLicenseType(Boolean.TRUE.equals(searchOwnerTrafficQualificationVO.getIsShangHaiLicense()) ? 0 : 1);

        SearchOwnerTrafficQualificationDTO searchOwnerTrafficQualificationDTO = ConvertUtil.convert(searchOwnerTrafficQualificationVO, SearchOwnerTrafficQualificationDTO.class);
        TrafficExportByte trafficExportByte = ownerTrafficQualificationService.exportOwnerTrafficQualification(searchOwnerTrafficQualificationDTO);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment;filename=" + System.currentTimeMillis() + ".xlsx");
        headers.add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        return new ResponseEntity<>(trafficExportByte.getBytes(), headers, HttpStatus.OK);
    }

    /**
     * 导入意向用户交通审核结果
     */
    @RequestLock
    @PostMapping("/importTrafficQualificationResult")
    @ApiOperation(value = "导入意向用户交通审核结果", httpMethod = "POST")
    public BaseResponse importTrafficQualificationResult(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession userSession) {
        UploadOwnerTrafficQualificationDTO uploadOwnerTrafficQualificationDTO = new UploadOwnerTrafficQualificationDTO();
        uploadOwnerTrafficQualificationDTO.setFile(file);
        uploadOwnerTrafficQualificationDTO.setUpdatedUserId(userSession.getUserId());
        uploadOwnerTrafficQualificationDTO.setUpdatedUserName(userSession.getUserName());
        ownerTrafficQualificationService.uploadOwnerTrafficQualification(uploadOwnerTrafficQualificationDTO);
        return new BaseResponse();
    }

    /**
     * 公安审批结果撤销
     *
     * @param cancelTrafficQualificationVO 记录
     * @return
     */
    @RequestLock
    @PostMapping("/cancelTrafficQualification")
    public BaseResponse cancelTrafficQualification(@RequestBody @Validated CancelTrafficQualificationVO cancelTrafficQualificationVO, @SessionAttribute("user") UserSession userSession) {
        CancelTrafficQualificationDTO cancelTrafficQualificationDTO = new CancelTrafficQualificationDTO();
        cancelTrafficQualificationDTO.setId(cancelTrafficQualificationVO.getId());
        cancelTrafficQualificationDTO.setUserId(userSession.getUserId());
        cancelTrafficQualificationDTO.setUserName(userSession.getUserName());
        return ownerTrafficQualificationService.cancelTrafficQualification(cancelTrafficQualificationDTO);
    }


}
