package com.extracme.nevmp.controller;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;
import org.springframework.web.multipart.MultipartFile;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.RequestLock;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.FileInfoBO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.owner.ImportOwnerReceiptDTO;
import com.extracme.nevmp.dto.receipt.ApproveOwnerReceiptDTO;
import com.extracme.nevmp.dto.receipt.DenyOwnerReceiptDTO;
import com.extracme.nevmp.dto.receipt.ImportOwnerReceiptCopyDTO;
import com.extracme.nevmp.dto.receipt.LockOwnerReceiptDTO;
import com.extracme.nevmp.dto.receipt.OwnerReceiptInfoDTO;
import com.extracme.nevmp.dto.receipt.QueryOwnerReceiptCopyDTO;
import com.extracme.nevmp.dto.receipt.SearchOwnerReceiptDTO;
import com.extracme.nevmp.dto.receipt.SearchOwnerReceiptUploadFileNotesDTO;
import com.extracme.nevmp.dto.receipt.SubmitOwnerReceiptDTO;
import com.extracme.nevmp.dto.receipt.UnlockOwnerReceiptDTO;
import com.extracme.nevmp.model.UploadFileNotes;
import com.extracme.nevmp.service.owner.OwnerReceiptService;
import com.extracme.nevmp.vo.receipt.ApproveOwnerReceiptVO;
import com.extracme.nevmp.vo.receipt.DenyOwnerReceiptVO;
import com.extracme.nevmp.vo.receipt.LockOwnerReceiptVO;
import com.extracme.nevmp.vo.receipt.QueryOwnerReceiptCopyVO;
import com.extracme.nevmp.vo.receipt.SearchOwnerReceiptUploadFileNotesVO;
import com.extracme.nevmp.vo.receipt.SearchOwnerReceiptVO;
import com.extracme.nevmp.vo.receipt.SubmitOwnerReceiptVO;
import com.extracme.nevmp.vo.receipt.UnlockOwnerReceiptVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 */
@Authorize
@RestController
@RequestMapping("/ownerReceipt")
@Api("用户购车发票服务")
public class OwnerReceiptController {


    @Autowired
    private OwnerReceiptService ownerReceiptService;

    @RequestLock
    @PostMapping("/importOwnerReceipt")
    @ApiOperation(value = "导入购车发票信息", httpMethod = "POST")
    public BaseResponse importOwnerReceipt(@RequestParam(value = "file") MultipartFile multipartFile, @SessionAttribute("user") UserSession user) {
        ImportOwnerReceiptDTO importOwnerReceiptDTO = new ImportOwnerReceiptDTO();
        importOwnerReceiptDTO.setMultipartFile(multipartFile);
        importOwnerReceiptDTO.setOrgId(user.getOrgId());
        importOwnerReceiptDTO.setOrgName(user.getOrgName());
        importOwnerReceiptDTO.setOperateUserId(user.getUserId());
        importOwnerReceiptDTO.setOperateUserName(user.getUserName());
        return ownerReceiptService.importOwnerReceipt(importOwnerReceiptDTO);
    }


    @PostMapping("/searchOwnerReceipt")
    @ApiOperation(value = "查询购车发票信息", httpMethod = "POST")
    public PageInfoBO<OwnerReceiptInfoDTO> searchOwnerReceipt(@RequestBody @Validated SearchOwnerReceiptVO searchOwnerReceiptVO, @SessionAttribute("user") UserSession userSession) {
        String orgId = userSession.getOrgId();
        SearchOwnerReceiptDTO searchOwnerReceiptDTO = ConvertUtil.convert(searchOwnerReceiptVO, SearchOwnerReceiptDTO.class);
        //覆盖用户输入的机构（只能查询当前登录账号相关信息）
        searchOwnerReceiptDTO.setOrgId(orgId);
        return ownerReceiptService.searchOwnerReceipt(searchOwnerReceiptDTO);
    }

    @PostMapping("/searchAllOwnerReceipt")
    @ApiOperation(value = "查询购车发票信息（经信委审核）", httpMethod = "POST")
    public PageInfoBO<OwnerReceiptInfoDTO> searchAllOwnerReceipt(@RequestBody @Validated SearchOwnerReceiptVO searchOwnerReceiptVO, @SessionAttribute("user") UserSession userSession){
        SearchOwnerReceiptDTO searchOwnerReceiptDTO = ConvertUtil.convert(searchOwnerReceiptVO, SearchOwnerReceiptDTO.class);
        return ownerReceiptService.searchOwnerReceipt(searchOwnerReceiptDTO);
    }

    @RequestLock
    @PostMapping("/importOwnerReceiptCopy")
    @ApiOperation(value = "导入购车发票扫描件", httpMethod = "POST")
    public BaseResponse importOwnerReceiptCopy(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession user){
        ImportOwnerReceiptCopyDTO importOwnerReceiptCopyDTO = new ImportOwnerReceiptCopyDTO();
        importOwnerReceiptCopyDTO.setMultipartFile(file);
        importOwnerReceiptCopyDTO.setOrgId(user.getOrgId());
        importOwnerReceiptCopyDTO.setUpdatedUserId(user.getUserId());
        importOwnerReceiptCopyDTO.setUpdatedUserName(user.getUserName());
        return ownerReceiptService.importOwnerReceiptCopy(importOwnerReceiptCopyDTO);
    }

    @RequestLock
    @PostMapping("/submit")
    @ApiOperation(value = "提交购车发票", httpMethod = "POST")
    public BaseResponse submit(@RequestBody @Validated SubmitOwnerReceiptVO submitOwnerReceiptVO, @SessionAttribute("user") UserSession user){
        SubmitOwnerReceiptDTO submitOwnerReceiptDTO = new SubmitOwnerReceiptDTO();
        submitOwnerReceiptDTO.setOwnerIds(submitOwnerReceiptVO.getOwnerIds());
        submitOwnerReceiptDTO.setOperateUserId(user.getUserId());
        submitOwnerReceiptDTO.setOperateUserName(user.getUserName());
        return ownerReceiptService.submit(submitOwnerReceiptDTO);
    }

    @PostMapping("/queryOwnerReceiptCopy")
    @ApiOperation(value = "查看购车发票照片", httpMethod = "POST")
    public FileInfoBO queryOwnerReceiptCopy(@RequestBody @Validated QueryOwnerReceiptCopyVO queryOwnerReceiptCopyVO){
        QueryOwnerReceiptCopyDTO queryOwnerReceiptCopyDTO = new QueryOwnerReceiptCopyDTO();
        queryOwnerReceiptCopyDTO.setOwnerId(queryOwnerReceiptCopyVO.getOwnerId());
        return ownerReceiptService.queryOwnerReceiptCopy(queryOwnerReceiptCopyDTO);
    }

    @PostMapping("/searchOwnerReceiptUploadFileNotes")
    @ApiOperation(value = "查询用户购车发票扫描件导入日志", httpMethod = "POST")
    public PageInfoBO<UploadFileNotes> searchOwnerReceiptUploadFileNotes(@RequestBody @Valid SearchOwnerReceiptUploadFileNotesVO searchOwnerReceiptUploadFileNotesVO, @SessionAttribute("user") UserSession user) {
        SearchOwnerReceiptUploadFileNotesDTO searchOwnerReceiptUploadFileNotesDTO = ConvertUtil.convert(searchOwnerReceiptUploadFileNotesVO, SearchOwnerReceiptUploadFileNotesDTO.class);
        //根据机构进行查询，统一机构内，日志数据共享
        searchOwnerReceiptUploadFileNotesDTO.setOrgId(user.getOrgId());
        return ownerReceiptService.searchOwnerReceiptUploadFileNotes(searchOwnerReceiptUploadFileNotesDTO);
    }

    @PostMapping("/searchOwnerReceiptInfoUploadFileNotes")
    @ApiOperation(value = "查询用户购车发票信息导入日志", httpMethod = "POST")
    public PageInfoBO<UploadFileNotes> searchOwnerReceiptInfoUploadFileNotes(@RequestBody @Valid SearchOwnerReceiptUploadFileNotesVO searchOwnerReceiptUploadFileNotesVO, @SessionAttribute("user") UserSession user){
        SearchOwnerReceiptUploadFileNotesDTO searchOwnerReceiptUploadFileNotesDTO = ConvertUtil.convert(searchOwnerReceiptUploadFileNotesVO, SearchOwnerReceiptUploadFileNotesDTO.class);
        //根据机构进行查询，统一机构内，日志数据共享
        searchOwnerReceiptUploadFileNotesDTO.setOrgId(user.getOrgId());
        return ownerReceiptService.searchOwnerReceiptInfoUploadFileNotes(searchOwnerReceiptUploadFileNotesDTO);
    }

    @RequestLock
    @PostMapping("/approve")
    @ApiOperation(value = "购车发票审核通过", httpMethod = "POST")
    public BaseResponse approve(@RequestBody @Valid ApproveOwnerReceiptVO approveOwnerReceiptVO, @SessionAttribute("user") UserSession user){
        ApproveOwnerReceiptDTO approveOwnerReceiptDTO = new ApproveOwnerReceiptDTO();
        approveOwnerReceiptDTO.setOwnerIds(approveOwnerReceiptVO.getOwnerIds());
        approveOwnerReceiptDTO.setUpdatedUserId(user.getUserId());
        approveOwnerReceiptDTO.setUpdatedUserName(user.getUserName());
        return ownerReceiptService.approve(approveOwnerReceiptDTO);
    }

    @RequestLock
    @PostMapping("/deny")
    @ApiOperation(value = "购车发票审核拒绝", httpMethod = "POST")
    public BaseResponse deny(@RequestBody @Valid DenyOwnerReceiptVO denyOwnerReceiptVO,@SessionAttribute("user") UserSession user){
        DenyOwnerReceiptDTO denyOwnerReceiptDTO = new DenyOwnerReceiptDTO();
        denyOwnerReceiptDTO.setOwnerIds(denyOwnerReceiptVO.getOwnerIds());
        denyOwnerReceiptDTO.setReason(denyOwnerReceiptVO.getReason());
        denyOwnerReceiptDTO.setUpdatedUserId(user.getUserId());
        denyOwnerReceiptDTO.setUpdatedUserName(user.getUserName());
        return ownerReceiptService.deny(denyOwnerReceiptDTO);
    }

    @RequestLock
    @PostMapping("/unlock")
    @ApiOperation(value = "购车发票解锁（允许修改）", httpMethod = "POST")
    public BaseResponse unlock(@RequestBody @Valid UnlockOwnerReceiptVO unlockOwnerReceiptVO, @SessionAttribute("user") UserSession user){
        UnlockOwnerReceiptDTO unlockOwnerReceiptDTO = new UnlockOwnerReceiptDTO();
        unlockOwnerReceiptDTO.setOwnerId(unlockOwnerReceiptVO.getOwnerId());
        unlockOwnerReceiptDTO.setUpdatedUserId(user.getUserId());
        unlockOwnerReceiptDTO.setUpdatedUserName(user.getUserName());
        return ownerReceiptService.unlock(unlockOwnerReceiptDTO);
    }

    @RequestLock
    @PostMapping("/lock")
    @ApiOperation(value = "购车发票解锁（不允许修改）", httpMethod = "POST")
    public BaseResponse lock(@RequestBody @Valid LockOwnerReceiptVO lockOwnerReceiptVO, @SessionAttribute("user") UserSession user){
        LockOwnerReceiptDTO lockOwnerReceiptDTO = new LockOwnerReceiptDTO();
        lockOwnerReceiptDTO.setOwnerId(lockOwnerReceiptVO.getOwnerId());
        lockOwnerReceiptDTO.setUpdatedUserId(user.getUserId());
        lockOwnerReceiptDTO.setUpdatedUserName(user.getUserName());
        return ownerReceiptService.lock(lockOwnerReceiptDTO);
    }


}
