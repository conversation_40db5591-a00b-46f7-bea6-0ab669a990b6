package com.extracme.nevmp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.MultiStrIdsDTO;
import com.extracme.nevmp.dto.vehicle.seller.manage.ApproveVehicleSellerManageDTO;
import com.extracme.nevmp.dto.vehicle.seller.manage.BackVehicleSellerManageDTO;
import com.extracme.nevmp.dto.vehicle.seller.manage.BatchBackVehicleSellerManageDTO;
import com.extracme.nevmp.dto.vehicle.seller.manage.DeleteVehicleSellerManageDTO;
import com.extracme.nevmp.dto.vehicle.seller.manage.DenyVehicleSellerManageDTO;
import com.extracme.nevmp.dto.vehicle.seller.manage.SaveVehicleSellerManageDTO;
import com.extracme.nevmp.dto.vehicle.seller.manage.SearchApproveVehicleSellerManageDTO;
import com.extracme.nevmp.dto.vehicle.seller.manage.SearchVehicleSellerManageDTO;
import com.extracme.nevmp.dto.vehicle.seller.manage.UpdateVehicleSellerManageDTO;
import com.extracme.nevmp.service.VehicleDealerCooperateService;
import com.extracme.nevmp.utils.CommonUtil;
import com.extracme.nevmp.vo.ApproveVehicleSellerManageVO;
import com.extracme.nevmp.vo.BackVehicleSellerManageVO;
import com.extracme.nevmp.vo.BatchBackVehicleSellerManageVO;
import com.extracme.nevmp.vo.common.MultiStrIdsVO;
import com.extracme.nevmp.vo.vehicle.seller.manage.DeleteVehicleSellerManageVO;
import com.extracme.nevmp.vo.vehicle.seller.manage.DenyVehicleSellerManageVO;
import com.extracme.nevmp.vo.vehicle.seller.manage.SaveVehicleSellerManageVO;
import com.extracme.nevmp.vo.vehicle.seller.manage.SearchApproveVehicleSellerManageVO;
import com.extracme.nevmp.vo.vehicle.seller.manage.SearchVehicleSellerManageVO;
import com.extracme.nevmp.vo.vehicle.seller.manage.UpdateVehicleSellerManageVO;

@Authorize
@RestController
@RequestMapping("vehicleSeller")
public class VehicleSellerManageController {

//    @Autowired
//    VehicleSellerManageService vehicleSellerManageService;

    @Autowired
    VehicleDealerCooperateService vehicleDealerCooperateService;

    /**
     * 保存经销商关联车型信息
     * @param saveVehicleSellerManageVO
     * @param userSession
     * @return
     */
    @PostMapping("saveVehicleSellerManage")
    public BaseResponse saveVehicleSellerManage(@RequestBody @Validated SaveVehicleSellerManageVO saveVehicleSellerManageVO, @SessionAttribute("user") UserSession userSession) {

        SaveVehicleSellerManageDTO saveVehicleSellerManageDTO = ConvertUtil.normalConvert(saveVehicleSellerManageVO, SaveVehicleSellerManageDTO.class);
        saveVehicleSellerManageDTO.setOrgId(userSession.getOrgId());
        saveVehicleSellerManageDTO.setOrgName(userSession.getOrgName());
        saveVehicleSellerManageDTO.setUserId(userSession.getUserId());
        saveVehicleSellerManageDTO.setUserName(userSession.getUserName());
        return vehicleDealerCooperateService.saveVehicleSellerManage(saveVehicleSellerManageDTO);
    }

    /**
     * 更新经销商关联车型信息
     * @param updateVehicleSellerManageVO
     * @param userSession
     * @return
     */
    @PostMapping("updateVehicleSellerManage")
    public BaseResponse updateVehicleSellerManage(@RequestBody @Validated UpdateVehicleSellerManageVO updateVehicleSellerManageVO, @SessionAttribute("user") UserSession userSession) {
        UpdateVehicleSellerManageDTO updateVehicleSellerManageDTO = ConvertUtil.convert(updateVehicleSellerManageVO, UpdateVehicleSellerManageDTO.class);
        updateVehicleSellerManageDTO.setOrgId(userSession.getOrgId());
        updateVehicleSellerManageDTO.setOrgName(userSession.getOrgName());
        updateVehicleSellerManageDTO.setUserId(userSession.getUserId());
        updateVehicleSellerManageDTO.setUserName(userSession.getUserName());
        return vehicleDealerCooperateService.updateVehicleSellerManage(updateVehicleSellerManageDTO);
    }

    /**
     * 删除经销商关联车型信息
     * @param deleteVehicleSellerManageVO
     * @param userSession
     * @return
     */
    @PostMapping("deleteVehicleSellerManage")
    public BaseResponse deleteVehicleSellerManage(@RequestBody @Validated DeleteVehicleSellerManageVO deleteVehicleSellerManageVO, @SessionAttribute("user") UserSession userSession) {
        DeleteVehicleSellerManageDTO deleteVehicleSellerManageDTO = ConvertUtil.normalConvert(deleteVehicleSellerManageVO, DeleteVehicleSellerManageDTO.class);
        return vehicleDealerCooperateService.deleteVehicleSellerManage(deleteVehicleSellerManageDTO);
    }

    /**
     * 提交经销商关联车型信息
     * @param multiStrIdsVO
     * @param userSession
     * @return
     */
    @PostMapping("submitVehicleSellerManage")
    public BaseResponse submitVehicleSellerManage(@RequestBody MultiStrIdsVO multiStrIdsVO, @SessionAttribute("user") UserSession userSession) {
        MultiStrIdsDTO multiStrIdsDTO = new MultiStrIdsDTO();
        multiStrIdsDTO.setIds(multiStrIdsVO.getIds());
        multiStrIdsDTO.setOrgId(userSession.getOrgId());
        multiStrIdsDTO.setOrgName(userSession.getOrgName());
        multiStrIdsDTO.setUserId(userSession.getUserId());
        multiStrIdsDTO.setUserName(userSession.getUserName());
        return vehicleDealerCooperateService.submitVehicleSellerManage(multiStrIdsDTO);
    }


    /**
     * 查询经销商关联车型信息
     * @param searchVehicleSellerManageVO
     * @param userSession
     * @return
     */
    @PostMapping("searchVehicleSellerManage")
    public BaseResponse searchVehicleSellerManage(@RequestBody SearchVehicleSellerManageVO searchVehicleSellerManageVO, @SessionAttribute("user") UserSession userSession) {
        SearchVehicleSellerManageDTO searchVehicleSellerManageDTO = ConvertUtil.convert(searchVehicleSellerManageVO, SearchVehicleSellerManageDTO.class);
        searchVehicleSellerManageDTO.setOffset(CommonUtil.getOffset(searchVehicleSellerManageVO.getPageNum(), searchVehicleSellerManageVO.getPageSize()));
        searchVehicleSellerManageDTO.setLimit(searchVehicleSellerManageVO.getPageSize());
        searchVehicleSellerManageDTO.setOrgId(userSession.getOrgId());
        return vehicleDealerCooperateService.searchVehicleSellerManage(searchVehicleSellerManageDTO);
    }


    /**
     * 查询提交后的经销商关联车型信息
     * @param searchApproveVehicleSellerManageVO
     * @return
     */
    @PostMapping("searchApproveVehicleSellerManage")
    public BaseResponse searchApproveVehicleSellerManage(@RequestBody SearchApproveVehicleSellerManageVO searchApproveVehicleSellerManageVO) {
        SearchApproveVehicleSellerManageDTO searchApproveVehicleSellerManageDTO = ConvertUtil.convert(searchApproveVehicleSellerManageVO, SearchApproveVehicleSellerManageDTO.class);
        searchApproveVehicleSellerManageDTO.setOffset(CommonUtil.getOffset(searchApproveVehicleSellerManageVO.getPageNum(), searchApproveVehicleSellerManageVO.getPageSize()));
        searchApproveVehicleSellerManageDTO.setLimit(searchApproveVehicleSellerManageVO.getPageSize());
        return vehicleDealerCooperateService.searchApproveVehicleSellerManage(searchApproveVehicleSellerManageDTO);
    }


    /**
     * 审批通过经销商关联车型信息
     * @param approveVehicleSellerManageVO
     * @param userSession
     * @return
     */
    @PostMapping("approveVehicleSellerManage")
    BaseResponse approveVehicleSellerManage(@RequestBody ApproveVehicleSellerManageVO approveVehicleSellerManageVO, @SessionAttribute("user") UserSession userSession) {
        ApproveVehicleSellerManageDTO approveVehicleSellerManageDTO = new ApproveVehicleSellerManageDTO();
        approveVehicleSellerManageDTO.setId(approveVehicleSellerManageVO.getIds());
        approveVehicleSellerManageDTO.setUserId(userSession.getUserId());
        approveVehicleSellerManageDTO.setUserName(userSession.getUserName());

        return vehicleDealerCooperateService.approveVehicleSellerManage(approveVehicleSellerManageDTO);
    }

    /**
     * 审批拒绝
     * @param denyVehicleSellerManageVO
     * @param userSession
     * @return
     */
    @PostMapping("denyApproveVehicleSellerManage")
    BaseResponse denyApproveVehicleSellerManage(@RequestBody DenyVehicleSellerManageVO denyVehicleSellerManageVO, @SessionAttribute("user") UserSession userSession) {
        DenyVehicleSellerManageDTO denyVehicleSellerManageDTO = new DenyVehicleSellerManageDTO();
        denyVehicleSellerManageDTO.setId(denyVehicleSellerManageVO.getIds());
        denyVehicleSellerManageDTO.setReason(denyVehicleSellerManageVO.getReason());
        denyVehicleSellerManageDTO.setUserId(userSession.getUserId());
        denyVehicleSellerManageDTO.setUserName(userSession.getUserName());

        return vehicleDealerCooperateService.denyApproveVehicleSellerManage(denyVehicleSellerManageDTO);
    }

    /**
     * 撤销经销商关联车型信息
     * @param backVehicleSellerManageVO
     * @param userSession
     * @return
     */
    @PostMapping("backApproveVehicleSellerManage")
    BaseResponse backApproveVehicleSellerManage(@RequestBody BackVehicleSellerManageVO backVehicleSellerManageVO,  @SessionAttribute("user") UserSession userSession) {
        BackVehicleSellerManageDTO backVehicleSellerManageDTO = new BackVehicleSellerManageDTO();
        backVehicleSellerManageDTO.setId(backVehicleSellerManageVO.getId());
        backVehicleSellerManageDTO.setUserId(userSession.getUserId());
        backVehicleSellerManageDTO.setUserName(userSession.getUserName());

        return vehicleDealerCooperateService.backApproveVehicleSellerManage(backVehicleSellerManageDTO);
    }


    /**
     * 批量撤销经销商关联车型信息
     * @return
     */
    @PostMapping("batchBackApproveVehicleSellerManage")
    BaseResponse batchBackApproveVehicleSellerManage(@RequestBody BatchBackVehicleSellerManageVO batchBackVehicleSellerManageVO, @SessionAttribute("user") UserSession userSession){
        BatchBackVehicleSellerManageDTO batchBackVehicleSellerManageDTO = new BatchBackVehicleSellerManageDTO();
        batchBackVehicleSellerManageDTO.setIds(batchBackVehicleSellerManageVO.getIds());
        batchBackVehicleSellerManageDTO.setUserId(userSession.getUserId());
        batchBackVehicleSellerManageDTO.setUserName(userSession.getUserName());
        return vehicleDealerCooperateService.batchBackApproveVehicleSellerManage(batchBackVehicleSellerManageDTO);
    }


}
