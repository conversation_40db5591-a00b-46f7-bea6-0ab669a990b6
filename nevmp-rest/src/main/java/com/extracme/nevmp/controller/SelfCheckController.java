package com.extracme.nevmp.controller;


import java.util.Date;
import java.util.concurrent.TimeUnit;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;
import org.springframework.web.multipart.MultipartFile;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.owner.SearchChargeConfirmUploadFileNotesDTO;
import com.extracme.nevmp.dto.owner.selfcheck.BatchUploadSelfCheckDTO;
import com.extracme.nevmp.dto.owner.selfcheck.CancelSelfCheckDTO;
import com.extracme.nevmp.dto.owner.selfcheck.CommitSelfCheckDTO;
import com.extracme.nevmp.dto.owner.selfcheck.DenySelfCheckDTO;
import com.extracme.nevmp.dto.owner.selfcheck.SearchCommitSelfCheckOwnerDTO;
import com.extracme.nevmp.dto.owner.selfcheck.SearchSelfCheckOwnerDTO;
import com.extracme.nevmp.dto.owner.selfcheck.UploadSelfCheckDTO;
import com.extracme.nevmp.service.owner.SelfCheckService;
import com.extracme.nevmp.utils.CommonUtil;
import com.extracme.nevmp.vo.SearchCommitSelfCheckOwnerVO;
import com.extracme.nevmp.vo.common.MultiLongId;
import com.extracme.nevmp.vo.common.SingleId;
import com.extracme.nevmp.vo.owner.SearchChargeConfirmUploadFileNotesVO;
import com.extracme.nevmp.vo.owner.selfcheck.DenySelfCheckVO;
import com.extracme.nevmp.vo.owner.selfcheck.SearchSelfCheckOwnerVO;
import com.extracme.nevmp.vo.owner.selfcheck.UploadSelfCheckVO;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("selfcheck")
@Authorize
public class SelfCheckController {

    @Autowired
    private SelfCheckService selfCheckService;



    @PostMapping("searchSelfCheckOwner")
    @ApiOperation(value = "查询待上传自查反馈的充电桩", httpMethod = "POST")
    public BaseResponse searchSelfCheckOwner(@RequestBody SearchSelfCheckOwnerVO searchSelfCheckOwnerVO, @SessionAttribute("user") UserSession user) {
        SearchSelfCheckOwnerDTO searchSelfCheckOwnerDTO = ConvertUtil.convert(searchSelfCheckOwnerVO, SearchSelfCheckOwnerDTO.class);
        if (searchSelfCheckOwnerDTO.getChargeAuditEndTime() != null) {
            searchSelfCheckOwnerDTO.setChargeAuditEndTime(new Date(searchSelfCheckOwnerDTO.getChargeAuditEndTime().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        searchSelfCheckOwnerDTO.setOrgId(user.getOrgId());
        searchSelfCheckOwnerDTO.setLimit(searchSelfCheckOwnerVO.getPageSize());
        searchSelfCheckOwnerDTO.setOffset(CommonUtil.getOffset(searchSelfCheckOwnerVO.getPageNum(), searchSelfCheckOwnerVO.getPageSize()));
        return selfCheckService.searchSelfCheckOwner(searchSelfCheckOwnerDTO);
    }


    @PostMapping("searchCommitSelfCheckOwner")
    @ApiOperation(value = "查询待审核自查反馈的充电桩", httpMethod = "POST")
    public BaseResponse searchCommitSelfCheckOwner(@RequestBody SearchCommitSelfCheckOwnerVO searchSelfCheckOwnerVO) {
        SearchCommitSelfCheckOwnerDTO searchSelfCheckOwnerDTO = ConvertUtil.convert(searchSelfCheckOwnerVO, SearchCommitSelfCheckOwnerDTO.class);
        if (searchSelfCheckOwnerDTO.getChargeAuditEndTime() != null) {
            searchSelfCheckOwnerDTO.setChargeAuditEndTime(new Date(searchSelfCheckOwnerDTO.getChargeAuditEndTime().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        searchSelfCheckOwnerDTO.setLimit(searchSelfCheckOwnerVO.getPageSize());
        searchSelfCheckOwnerDTO.setOffset(CommonUtil.getOffset(searchSelfCheckOwnerVO.getPageNum(), searchSelfCheckOwnerVO.getPageSize()));
        return selfCheckService.searchCommitSelfCheckOwner(searchSelfCheckOwnerDTO);
    }

    @PostMapping("exportSelfCheckOwner")
    @ApiOperation(value = "导出待上传自查反馈的充电桩", httpMethod = "POST")
    public HttpEntity<byte[]> exportSelfCheckOwner(@RequestBody SearchSelfCheckOwnerVO searchSelfCheckOwnerVO, @SessionAttribute("user") UserSession user) {
        SearchSelfCheckOwnerDTO searchSelfCheckOwnerDTO = ConvertUtil.convert(searchSelfCheckOwnerVO, SearchSelfCheckOwnerDTO.class);
        if (searchSelfCheckOwnerDTO.getChargeAuditEndTime() != null) {
            searchSelfCheckOwnerDTO.setChargeAuditEndTime(new Date(searchSelfCheckOwnerDTO.getChargeAuditEndTime().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        searchSelfCheckOwnerDTO.setOrgId(user.getOrgId());
         byte[] bytes = selfCheckService.exportSelfCheckOwner(searchSelfCheckOwnerDTO);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment;filename=" + System.currentTimeMillis() + ".pdf");
        headers.add("Content-Type", "application/pdf;charset=utf-8");
        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }


    @PostMapping("uploadSelfCheck")
    @ApiOperation(value = "上传自查反馈表", httpMethod = "POST")
    public BaseResponse uploadSelfCheck(@RequestBody @Validated UploadSelfCheckVO uploadSelfCheckVO, @SessionAttribute("user") UserSession user) {
        UploadSelfCheckDTO uploadSelfCheckDTO = ConvertUtil.normalConvert(uploadSelfCheckVO, UploadSelfCheckDTO.class);
        uploadSelfCheckDTO.setUserId(user.getUserId());
        uploadSelfCheckDTO.setUserName(user.getUserName());
        uploadSelfCheckDTO.setOrgId(user.getOrgId());
        return selfCheckService.uploadSelfCheck(uploadSelfCheckDTO);
    }


    @PostMapping("batchUploadSelfCheck")
    @ApiOperation(value = "批量上传自查反馈表")
    public BaseResponse batchUploadSelfCheck(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession userSession) {
        BatchUploadSelfCheckDTO batchUploadSelfCheckDTO = new BatchUploadSelfCheckDTO();
        batchUploadSelfCheckDTO.setFile(file);
        batchUploadSelfCheckDTO.setUserId(userSession.getUserId());
        batchUploadSelfCheckDTO.setUserName(userSession.getUserName());
        batchUploadSelfCheckDTO.setOrgId(userSession.getOrgId());
        return selfCheckService.batchUploadSelfCheck(batchUploadSelfCheckDTO);
    }

    @PostMapping("uploadChargeNameplate")
    @ApiOperation(value = "上传充电桩铭牌")
    public BaseResponse uploadChargeNameplate(@RequestBody @Validated UploadSelfCheckVO uploadSelfCheckVO, @SessionAttribute("user") UserSession user) {
        UploadSelfCheckDTO uploadSelfCheckDTO = ConvertUtil.normalConvert(uploadSelfCheckVO, UploadSelfCheckDTO.class);
        uploadSelfCheckDTO.setUserId(user.getUserId());
        uploadSelfCheckDTO.setUserName(user.getUserName());
        uploadSelfCheckDTO.setOrgId(user.getOrgId());
        return selfCheckService.uploadChargeNameplate(uploadSelfCheckDTO);
    }

    @PostMapping("batchUploadChargeNameplate")
    @ApiOperation(value = "批量上传充电桩铭牌")
    public BaseResponse batchUploadChargeNameplate(@RequestParam("file") MultipartFile file, @SessionAttribute("user") UserSession userSession) {
        BatchUploadSelfCheckDTO batchUploadSelfCheckDTO = new BatchUploadSelfCheckDTO();
        batchUploadSelfCheckDTO.setFile(file);
        batchUploadSelfCheckDTO.setUserId(userSession.getUserId());
        batchUploadSelfCheckDTO.setUserName(userSession.getUserName());
        batchUploadSelfCheckDTO.setOrgId(userSession.getOrgId());
        return selfCheckService.batchUploadChargeNameplate(batchUploadSelfCheckDTO);
    }


    @PostMapping("commitSelfCheck")
    @ApiOperation(value = "提交自查反馈表", httpMethod = "POST")
    public BaseResponse commitSelfCheck(@RequestBody MultiLongId ids, @SessionAttribute("user") UserSession user) {
        Assert.notEmpty(ids.getIds(), "至少选择一条记录");
        CommitSelfCheckDTO commitSelfCheckDTO = new CommitSelfCheckDTO();
        commitSelfCheckDTO.setIds(ids.getIds());
        commitSelfCheckDTO.setOrgId(user.getOrgId());
        commitSelfCheckDTO.setUserId(user.getUserId());
        commitSelfCheckDTO.setUserName(user.getUserName());
        return selfCheckService.commitSelfCheck(commitSelfCheckDTO);
    }

    @PostMapping("approve")
    @ApiOperation(value = "审核通过", httpMethod = "POST")
    public BaseResponse approve(@RequestBody MultiLongId ids, @SessionAttribute("user") UserSession user) {
        Assert.notEmpty(ids.getIds(), "至少选择一条记录");
        return selfCheckService.approve(ids.getIds(), user.getUserId(), user.getUserName());
    }


    @PostMapping("deny")
    @ApiOperation(value = "审核拒绝", httpMethod = "POST")
    public BaseResponse deny(@RequestBody @Validated DenySelfCheckVO denySelfCheckVO, @SessionAttribute("user") UserSession user) {
        DenySelfCheckDTO denySelfCheckDTO = new DenySelfCheckDTO();
        denySelfCheckDTO.setIds(denySelfCheckVO.getIds());
        denySelfCheckDTO.setReason(denySelfCheckVO.getReason());
        denySelfCheckDTO.setUserId(user.getUserId());
        denySelfCheckDTO.setUserName(user.getUserName());
        return selfCheckService.deny(denySelfCheckDTO);
    }


    @PostMapping("cancel")
    @ApiOperation(value = "撤销")
    public BaseResponse cancel(@RequestBody @Validated SingleId<Long> cancelSelfCheckVO, @SessionAttribute("user") UserSession user) {
        CancelSelfCheckDTO cancelSelfCheckDTO = new CancelSelfCheckDTO();
        cancelSelfCheckDTO.setId(cancelSelfCheckVO.getId());
        cancelSelfCheckDTO.setUserId(user.getUserId());
        cancelSelfCheckDTO.setUserName(user.getUserName());
        return selfCheckService.cancel(cancelSelfCheckDTO);
    }

    @PostMapping("getSelfCheckCopy")
    @Authorize(accessAuthorize = false)
    @ApiOperation(value = "获取自查反馈图片", httpMethod = "POST")
    public BaseResponse getSelfCheckCopy(@RequestBody SingleId<Long> id) {
        return selfCheckService.getSelfCheckCopy(id.getId());
    }

    @PostMapping("uploadFileNotes")
    public BaseResponse uploadFileNotes(@RequestBody @Valid SearchChargeConfirmUploadFileNotesVO searchChargeConfirmUploadFileNotesVO, @SessionAttribute("user") UserSession user) {
        SearchChargeConfirmUploadFileNotesDTO searchChargeConfirmUploadFileNotesDTO = ConvertUtil.convert(searchChargeConfirmUploadFileNotesVO, SearchChargeConfirmUploadFileNotesDTO.class);
        //根据机构进行查询，统一机构内，日志数据共享
        searchChargeConfirmUploadFileNotesDTO.setOrgId(user.getOrgId());
        return selfCheckService.searchSelfCheckUploadFileNotes(searchChargeConfirmUploadFileNotesDTO);
    }

    @ApiOperation("查看充电桩铭牌上传日志")
    @PostMapping("searchChargeNameplateNote")
    public BaseResponse searchChargeNameplateNote(@RequestBody @Valid SearchChargeConfirmUploadFileNotesVO searchChargeConfirmUploadFileNotesVO, @SessionAttribute("user") UserSession user) {
        SearchChargeConfirmUploadFileNotesDTO searchChargeConfirmUploadFileNotesDTO = ConvertUtil.convert(searchChargeConfirmUploadFileNotesVO, SearchChargeConfirmUploadFileNotesDTO.class);
        //根据机构进行查询，统一机构内，日志数据共享
        searchChargeConfirmUploadFileNotesDTO.setOrgId(user.getOrgId());
        return selfCheckService.searchChargeNameplateNote(searchChargeConfirmUploadFileNotesDTO);
    }
}
