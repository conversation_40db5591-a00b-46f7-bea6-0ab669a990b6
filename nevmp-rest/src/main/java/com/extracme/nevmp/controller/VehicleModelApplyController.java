package com.extracme.nevmp.controller;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.auth.Authorize;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.common.FileInfoBO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.file.FileResponse;
import com.extracme.nevmp.dto.vehicle.model.*;
import com.extracme.nevmp.model.VehicleModel2017;
import com.extracme.nevmp.model.VehicleModelOperateLog;
import com.extracme.nevmp.service.vehicle.VehicleModelApplyService;
import com.extracme.nevmp.utils.AssertUtil;
import com.extracme.nevmp.utils.CommonUtil;
import com.extracme.nevmp.vo.DownloadParamVO;
import com.extracme.nevmp.vo.ModelRemarkVO;
import com.extracme.nevmp.vo.ModelTerminateVO;
import com.extracme.nevmp.vo.common.MultiId;
import com.extracme.nevmp.vo.vehicle.model.SearchVehicleModelApplyVO;
import com.extracme.nevmp.vo.common.SingleId;
import com.extracme.nevmp.vo.vehicle.model.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 车型审核，正向流程：
 * 1. 车企， 上传申请书和证明材料 ， 提交审批
 * 2. 数据中心， 上传证明材料， 进行对接审批
 * 3. 检测中心，上传评审报告和车型参数表， 进行资料确认审核， 失败进入资料退回， 车企重新提交后，进入对接成功。
 * 4. 检测中心， 进行评审报告确认审核
 * 5. 经信委，  进行上牌审核。 失败进入资料退回
 * <AUTHOR>
 * @date 2020/11/4
 */
@Authorize
@RestController
@RequestMapping(value = "vehicleModelApply")
public class VehicleModelApplyController {

    @Autowired
    private VehicleModelApplyService vehicleModelApplyService;

    @ApiOperation(httpMethod = "POST", value = "新增乘用车型")
    @RequestMapping("addPassengerVehicleModel")
    public BaseResponse addPassengerVehicleModel(@RequestBody @Validated PassengerVehicleModelVO vehicleModelVO, @SessionAttribute("user")UserSession user) {
        VehicleModel2017 vehicleModel2017 = new VehicleModel2017();
        BeanUtils.copyProperties(vehicleModelVO, vehicleModel2017);
        vehicleModel2017.setVehicleModelId(vehicleModelVO.getVehicleModelId().trim());
        vehicleModel2017.setOrgId(user.getOrgId());
        vehicleModel2017.setOrgName(user.getOrgName());
        vehicleModel2017.setVehicleType(1);
        vehicleModel2017.setCreatedTime(new Date());
        vehicleModel2017.setCreatedUser(user.getUserId());
        vehicleModel2017.setCreatedUserName(user.getUserName());
        vehicleModel2017.setUpdatedTime(new Date());
        vehicleModel2017.setUpdatedUser(user.getUserId());
        vehicleModel2017.setUpdatedUserName(user.getUserName());
        return vehicleModelApplyService.addVehicleModel(vehicleModel2017);
    }

    @ApiOperation(httpMethod = "POST", value = "新增商用车型")
    @RequestMapping("addCommercialVehicleModel")
    public BaseResponse addCommercialVehicleModel(@RequestBody @Validated CommercialVehicleModelVO vehicleModelVO, @SessionAttribute("user")UserSession user) {
        VehicleModel2017 vehicleModel2017 = new VehicleModel2017();
        BeanUtils.copyProperties(vehicleModelVO, vehicleModel2017);
        vehicleModel2017.setVehicleModelId(vehicleModelVO.getVehicleModelId().trim());
        vehicleModel2017.setOrgId(user.getOrgId());
        vehicleModel2017.setOrgName(user.getOrgName());
        vehicleModel2017.setVehicleType(2);
        vehicleModel2017.setCreatedTime(new Date());
        vehicleModel2017.setCreatedUser(user.getUserId());
        vehicleModel2017.setCreatedUserName(user.getUserName());
        vehicleModel2017.setUpdatedTime(new Date());
        vehicleModel2017.setUpdatedUser(user.getUserId());
        vehicleModel2017.setUpdatedUserName(user.getUserName());
        return vehicleModelApplyService.addVehicleModel(vehicleModel2017);
    }

    @ApiOperation(httpMethod = "POST", value = "新增燃料车型")
    @RequestMapping("addFuelVehicleModel")
    public BaseResponse addFuelVehicleModel(@RequestBody @Validated FuelVehicleModelVO vehicleModelVO, @SessionAttribute("user")UserSession user) {
        VehicleModel2017 vehicleModel2017 = new VehicleModel2017();
        BeanUtils.copyProperties(vehicleModelVO, vehicleModel2017);
        vehicleModel2017.setVehicleModelId(vehicleModelVO.getVehicleModelId().trim());
        vehicleModel2017.setOrgId(user.getOrgId());
        vehicleModel2017.setOrgName(user.getOrgName());
        vehicleModel2017.setVehicleType(4);
        vehicleModel2017.setCreatedTime(new Date());
        vehicleModel2017.setCreatedUser(user.getUserId());
        vehicleModel2017.setCreatedUserName(user.getUserName());
        vehicleModel2017.setUpdatedTime(new Date());
        vehicleModel2017.setUpdatedUser(user.getUserId());
        vehicleModel2017.setUpdatedUserName(user.getUserName());
        return vehicleModelApplyService.addVehicleModel(vehicleModel2017);
    }

    @ApiOperation(httpMethod = "POST", value = "新增混动车型")
    @RequestMapping("addHybridVehicleModel")
    public BaseResponse addHybridVehicleModel(@RequestBody @Validated HybridVehicleModelVO vehicleModelVO, @SessionAttribute("user")UserSession user) {
        VehicleModel2017 vehicleModel2017 = new VehicleModel2017();
        BeanUtils.copyProperties(vehicleModelVO, vehicleModel2017);
        vehicleModel2017.setVehicleModelId(vehicleModelVO.getVehicleModelId().trim());
        vehicleModel2017.setOrgId(user.getOrgId());
        vehicleModel2017.setOrgName(user.getOrgName());
        vehicleModel2017.setVehicleType(3);
        vehicleModel2017.setCreatedTime(new Date());
        vehicleModel2017.setCreatedUser(user.getUserId());
        vehicleModel2017.setCreatedUserName(user.getUserName());
        vehicleModel2017.setUpdatedTime(new Date());
        vehicleModel2017.setUpdatedUser(user.getUserId());
        vehicleModel2017.setUpdatedUserName(user.getUserName());
        return vehicleModelApplyService.addVehicleModel(vehicleModel2017);
    }

    @ApiOperation(httpMethod = "POST", value = "获取车型详情")
    @PostMapping("getVehicleModelDetail")
    public BaseResponse getVehicleModelDetail(@RequestBody SingleId<String> id, @SessionAttribute("user") UserSession user) {
        AssertUtil.hasText(id.getId(), "车型编码不能为空");
        return vehicleModelApplyService.getVehicleModelDetail(id.getId(), user.getOrgId());
    }

    @ApiOperation(httpMethod = "POST", value = "车企搜索车型")
    @PostMapping("vehicle/searchVehicleModel")
    public BaseResponse searchVehicleModel(@RequestBody SearchVehicleModelApplyVO searchVO, @SessionAttribute("user") UserSession user) {
        SearchVehicleModelApplyDTO searchVehicleModelApplyDTO = ConvertUtil.convert(searchVO, SearchVehicleModelApplyDTO.class);
        searchVehicleModelApplyDTO.setOrgId(user.getOrgId());
        searchVehicleModelApplyDTO.setLimit(searchVO.getPageSize());
        searchVehicleModelApplyDTO.setOffset(CommonUtil.getOffset(searchVO.getPageNum(), searchVO.getPageSize()));
        return vehicleModelApplyService.searchVehicleModel(searchVehicleModelApplyDTO);
    }


    @ApiOperation(httpMethod = "POST", value = "更新乘用车型")
    @RequestMapping("updatePassengerVehicleModel")
    public BaseResponse updatePassengerVehicleModel(@RequestBody PassengerVehicleModelVO vehicleModelVO, @SessionAttribute("user")UserSession user) {
        VehicleModel2017 vehicleModel2017 = new VehicleModel2017();
        BeanUtils.copyProperties(vehicleModelVO, vehicleModel2017);
        vehicleModel2017.setVehicleModelId(vehicleModelVO.getVehicleModelId().trim());
        vehicleModel2017.setOrgId(user.getOrgId());
        vehicleModel2017.setOrgName(user.getOrgName());
        vehicleModel2017.setCreatedTime(new Date());
        vehicleModel2017.setCreatedUser(user.getUserId());
        vehicleModel2017.setCreatedUserName(user.getUserName());
        vehicleModel2017.setUpdatedTime(new Date());
        vehicleModel2017.setUpdatedUser(user.getUserId());
        vehicleModel2017.setUpdatedUserName(user.getUserName());
        return vehicleModelApplyService.updateVehicleModel(vehicleModel2017);
    }

    @ApiOperation(httpMethod = "POST", value = "更新商用车型")
    @RequestMapping("updateCommercialVehicleModel")
    public BaseResponse updateCommercialVehicleModel(@RequestBody CommercialVehicleModelVO vehicleModelVO, @SessionAttribute("user")UserSession user) {
        VehicleModel2017 vehicleModel2017 = new VehicleModel2017();
        BeanUtils.copyProperties(vehicleModelVO, vehicleModel2017);
        vehicleModel2017.setVehicleModelId(vehicleModelVO.getVehicleModelId().trim());
        vehicleModel2017.setOrgId(user.getOrgId());
        vehicleModel2017.setOrgName(user.getOrgName());
        vehicleModel2017.setCreatedTime(new Date());
        vehicleModel2017.setCreatedUser(user.getUserId());
        vehicleModel2017.setCreatedUserName(user.getUserName());
        vehicleModel2017.setUpdatedTime(new Date());
        vehicleModel2017.setUpdatedUser(user.getUserId());
        vehicleModel2017.setUpdatedUserName(user.getUserName());
        return vehicleModelApplyService.updateVehicleModel(vehicleModel2017);
    }

    @ApiOperation(httpMethod = "POST", value = "更新燃料车型")
    @RequestMapping("updateFuelVehicleModel")
    public BaseResponse updateFuelVehicleModel(@RequestBody FuelVehicleModelVO vehicleModelVO, @SessionAttribute("user")UserSession user) {
        VehicleModel2017 vehicleModel2017 = new VehicleModel2017();
        BeanUtils.copyProperties(vehicleModelVO, vehicleModel2017);
        vehicleModel2017.setVehicleModelId(vehicleModelVO.getVehicleModelId().trim());
        vehicleModel2017.setOrgId(user.getOrgId());
        vehicleModel2017.setOrgName(user.getOrgName());
        vehicleModel2017.setCreatedTime(new Date());
        vehicleModel2017.setCreatedUser(user.getUserId());
        vehicleModel2017.setCreatedUserName(user.getUserName());
        vehicleModel2017.setUpdatedTime(new Date());
        vehicleModel2017.setUpdatedUser(user.getUserId());
        vehicleModel2017.setUpdatedUserName(user.getUserName());
        return vehicleModelApplyService.updateVehicleModel(vehicleModel2017);
    }

    @ApiOperation(httpMethod = "POST", value = "更新混动车型")
    @RequestMapping("updateHybridVehicleModel")
    public BaseResponse updateHybridVehicleModel(@RequestBody HybridVehicleModelVO vehicleModelVO, @SessionAttribute("user")UserSession user) {
        VehicleModel2017 vehicleModel2017 = new VehicleModel2017();
        BeanUtils.copyProperties(vehicleModelVO, vehicleModel2017);
        vehicleModel2017.setVehicleModelId(vehicleModelVO.getVehicleModelId().trim());
        vehicleModel2017.setOrgId(user.getOrgId());
        vehicleModel2017.setOrgName(user.getOrgName());
        vehicleModel2017.setCreatedTime(new Date());
        vehicleModel2017.setCreatedUser(user.getUserId());
        vehicleModel2017.setCreatedUserName(user.getUserName());
        vehicleModel2017.setUpdatedTime(new Date());
        vehicleModel2017.setUpdatedUser(user.getUserId());
        vehicleModel2017.setUpdatedUserName(user.getUserName());
        return vehicleModelApplyService.updateVehicleModel(vehicleModel2017);
    }

    @ApiOperation(httpMethod = "POST", value = "删除车型")
    @RequestMapping("deleteVehicleModel")
    public BaseResponse deleteVehicleModel(@RequestBody SingleId<String> id, @SessionAttribute("user")UserSession user) {
        return vehicleModelApplyService.deleteVehicleModel(id.getId(), user.getOrgId());
    }

    @ApiOperation(httpMethod = "POST", value = "车企上传申请书")
    @RequestMapping("uploadApplyDoc")
    public BaseResponse uploadApplyDoc(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user") UserSession userSession) {
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setUserId(userSession.getUserId());
        uploadModelFileDTO.setUserName(userSession.getUserName());
        uploadModelFileDTO.setOrgId(userSession.getOrgId());
        vehicleModelApplyService.uploadApplyDoc(uploadModelFileDTO);
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "车企上传证明材料")
    @RequestMapping("uploadConfirmDoc")
    public BaseResponse uploadConfirmDoc(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user") UserSession userSession) {
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setUserId(userSession.getUserId());
        uploadModelFileDTO.setUserName(userSession.getUserName());
        uploadModelFileDTO.setOrgId(userSession.getOrgId());
        vehicleModelApplyService.uploadConfirmDoc(uploadModelFileDTO);
        return new BaseResponse();
    }


    @ApiOperation(httpMethod = "POST", value = "备注")
    @RequestMapping("remark")
    public BaseResponse remark(@RequestBody ModelRemarkVO remarkVO, @SessionAttribute("user") UserSession userSession) {
        ModelRemarkDTO modelRemarkDTO = ConvertUtil.convert(remarkVO, ModelRemarkDTO.class);
        modelRemarkDTO.setOrgId(userSession.getOrgId());
        modelRemarkDTO.setUserId(userSession.getUserId());
        modelRemarkDTO.setUserName(userSession.getUserName());
        return vehicleModelApplyService.remark(modelRemarkDTO);
    }

    @ApiOperation(httpMethod = "POST", value = "车企提交审批")
    @RequestMapping("commit")
    public BaseResponse commit(@RequestBody SingleId<String> commitId, @SessionAttribute("user") UserSession userSession) {
        CommitModelDTO commitModelDTO = new CommitModelDTO();
        commitModelDTO.setModelId(commitId.getId());
        commitModelDTO.setUserId(userSession.getUserId());
        commitModelDTO.setUserName(userSession.getUserName());
        commitModelDTO.setOrgId(userSession.getOrgId());
        return vehicleModelApplyService.commitOne(commitModelDTO);
    }


    @ApiOperation(httpMethod = "POST", value = "检测中心、数据中心、经信委 查询待审批车型列表")
    @PostMapping("approve/searchVehicleModel")
    public BaseResponse approveVehicleModel(@RequestBody SearchVehicleModelApplyVO searchVO) {
        SearchVehicleModelApplyDTO searchVehicleModelApplyDTO = ConvertUtil.convert(searchVO, SearchVehicleModelApplyDTO.class);
        if (searchVO.getVehicleFirstRegTimeEnd() != null) {
            searchVehicleModelApplyDTO.setVehicleFirstRegTimeEnd(new Date(searchVO.getVehicleFirstRegTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
        //对数据中心报告申请结束时间进行处理
        if (searchVO.getModelEvaluationDataSubmitTimeEnd() != null){
            searchVehicleModelApplyDTO.setModelEvaluationDataSubmitTimeEnd(new Date(searchVO.getModelEvaluationDataSubmitTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }

        //对检测中心报告申请结束时间进行处理
        if (searchVO.getModelEvaluationTestingSubmitTimeEnd() != null){
            searchVehicleModelApplyDTO.setModelEvaluationTestingSubmitTimeEnd(new Date(searchVO.getModelEvaluationTestingSubmitTimeEnd().getTime() + TimeUnit.DAYS.toMillis(1)));
        }

        searchVehicleModelApplyDTO.setOffset(CommonUtil.getOffset(searchVO.getPageNum(), searchVO.getPageSize()));
        searchVehicleModelApplyDTO.setLimit(searchVO.getPageSize());
        return vehicleModelApplyService.approveVehicleModel(searchVehicleModelApplyDTO);
    }


    @ApiOperation(httpMethod = "POST", value = "数据中心上传证明材料")
    @PostMapping("uploadCertificate")
    public BaseResponse uploadCertificate(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user")UserSession user) {
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setOrgId(user.getOrgId());
        uploadModelFileDTO.setUserId(user.getUserId());
        uploadModelFileDTO.setUserName(user.getUserName());
        return vehicleModelApplyService.uploadCertificate(uploadModelFileDTO);
    }

    @ApiOperation(httpMethod = "POST", value = "车企上传车型登记申请表")
    @RequestMapping("uploadModelRegisterApplication")
    public BaseResponse uploadModelRegisterApplication(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user")UserSession user) {
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setOrgId(user.getOrgId());
        uploadModelFileDTO.setUserId(user.getUserId());
        uploadModelFileDTO.setUserName(user.getUserName());
        vehicleModelApplyService.uploadModelRegisterApplication(uploadModelFileDTO);
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "车型受理")
    @PostMapping("accept")
    public BaseResponse accept(@RequestBody SingleId<String> id, @SessionAttribute("user")UserSession user){
        vehicleModelApplyService.accept(id.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "车型受理")
    @PostMapping("terminate")
    public BaseResponse terminate(@RequestBody ModelTerminateVO terminateVO, @SessionAttribute("user")UserSession user){
        vehicleModelApplyService.terminate(terminateVO.getId(), terminateVO.getRemark(), user.getUserId(), user.getUserName());
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "车型核实确认")
    @PostMapping("accessApprove")
    public BaseResponse accessApprove(@RequestBody SingleId<String> id, @SessionAttribute("user")UserSession user) {
        return vehicleModelApplyService.accessApprove(id.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
    }

    @ApiOperation(httpMethod = "POST", value = "车型核实退回")
    @PostMapping("accessDeny")
    public BaseResponse accessDeny(@RequestBody SingleId<String> id, @SessionAttribute("user")UserSession user) {
        return vehicleModelApplyService.accessDeny(id.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
    }


    @ApiOperation(httpMethod = "POST", value = "车型核实受理退回")
    @PostMapping("accessReturn")
    public BaseResponse accessReturn(@RequestBody SingleId<String> id, @SessionAttribute("user")UserSession user){
        vehicleModelApplyService.accessReturn(id.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "上传数据中心 评估报告")
    @RequestMapping("uploadDataCenterEvaluationReport")
    public BaseResponse uploadDataCenterEvaluationReport(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user")UserSession user){
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setOrgId(user.getOrgId());
        uploadModelFileDTO.setUserId(user.getUserId());
        uploadModelFileDTO.setUserName(user.getUserName());
        vehicleModelApplyService.uploadDataCenterEvaluationReport(uploadModelFileDTO);
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "上传检测中心 评估报告")
    @RequestMapping("uploadTestingCenterEvaluationReport")
    public BaseResponse uploadTestingCenterEvaluationReport(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user")UserSession user){
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setOrgId(user.getOrgId());
        uploadModelFileDTO.setUserId(user.getUserId());
        uploadModelFileDTO.setUserName(user.getUserName());
        vehicleModelApplyService.uploadTestingCenterEvaluationReport(uploadModelFileDTO);
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "提交数据中心评估")
    @PostMapping("submitDataCenterEvaluation")
    public BaseResponse submitDataCenterEvaluation(@RequestBody SingleId<String> id, @SessionAttribute("user")UserSession user){
        vehicleModelApplyService.submitDataCenterEvaluation(id.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "提交检测中心评估")
    @PostMapping("submitTestingCenterEvaluation")
    public BaseResponse submitTestingCenterEvaluation(@RequestBody SingleId<String> id, @SessionAttribute("user")UserSession user){
        vehicleModelApplyService.submitTestingCenterEvaluation(id.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "退回数据中心评估报告")
    @PostMapping("dataCenterEvaluationDeny")
    public BaseResponse dataCenterEvaluationDeny(@RequestBody SingleId<String> id, @SessionAttribute("user")UserSession user) {
        vehicleModelApplyService.dataCenterEvaluationDeny(id.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "退回检测中心评估报告")
    @PostMapping("testingCenterEvaluationDeny")
    public BaseResponse testingCenterEvaluationDeny(@RequestBody SingleId<String> id, @SessionAttribute("user")UserSession user) {
        vehicleModelApplyService.testingCenterEvaluationDeny(id.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
        return new BaseResponse();
    }

    @ApiOperation(httpMethod = "POST", value = "确认评估报告")
    @PostMapping("evaluationApprove")
    public BaseResponse evaluationApprove(@RequestBody SingleId<String> id, @SessionAttribute("user")UserSession user) {
        vehicleModelApplyService.evaluationApprove(id.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
        return new BaseResponse();
    }

    @Deprecated
    @ApiOperation(httpMethod = "POST", value = "车企上传平台符合性报告")
    @RequestMapping("uploadPlatformComplianceReport")
    public BaseResponse uploadPlatformComplianceReport(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user")UserSession user) {
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setOrgId(user.getOrgId());
        uploadModelFileDTO.setUserId(user.getUserId());
        uploadModelFileDTO.setUserName(user.getUserName());
        vehicleModelApplyService.uploadPlatformComplianceReport(uploadModelFileDTO);
        return new BaseResponse();
    }

    @Deprecated
    @ApiOperation(httpMethod = "POST", value = "检测中心上传车型评估报告")
    @RequestMapping("uploadModelEvaluationReport")
    public BaseResponse uploadModelEvaluationReport(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user")UserSession user) {
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setOrgId(user.getOrgId());
        uploadModelFileDTO.setUserId(user.getUserId());
        uploadModelFileDTO.setUserName(user.getUserName());
        vehicleModelApplyService.uploadModelEvaluationReport(uploadModelFileDTO);
        return new BaseResponse();
    }


    @Deprecated
    @ApiOperation(httpMethod = "POST", value = "检测中心上传评审报告")
    @RequestMapping("uploadReviewReport")
    public BaseResponse uploadReviewReport(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user")UserSession user) {
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setOrgId(user.getOrgId());
        uploadModelFileDTO.setUserId(user.getUserId());
        uploadModelFileDTO.setUserName(user.getUserName());
        return vehicleModelApplyService.uploadReviewReport(uploadModelFileDTO);
    }

    @Deprecated
    @ApiOperation(httpMethod = "POST", value = "检测中心上传质量抽查表")
    @RequestMapping("uploadCheckReport")
    public BaseResponse uploadCheckReport(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user")UserSession user) {
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setOrgId(user.getOrgId());
        uploadModelFileDTO.setUserId(user.getUserId());
        uploadModelFileDTO.setUserName(user.getUserName());
        return vehicleModelApplyService.uploadCheckReport(uploadModelFileDTO);
    }

    @Deprecated
    @ApiOperation(httpMethod = "POST", value = "检测中心上传车型参数表")
    @RequestMapping("uploadParamReport")
    public BaseResponse uploadParamReport(@RequestBody UploadModelFileVO uploadModelFileVO, @SessionAttribute("user")UserSession user) {
        UploadModelFileDTO uploadModelFileDTO = ConvertUtil.convert(uploadModelFileVO, UploadModelFileDTO.class);
        uploadModelFileDTO.setOrgId(user.getOrgId());
        uploadModelFileDTO.setUserId(user.getUserId());
        uploadModelFileDTO.setUserName(user.getUserName());
        return vehicleModelApplyService.uploadParamReport(uploadModelFileDTO);
    }


    @Deprecated
    @ApiOperation(httpMethod = "POST", value = "检测中心资料确认")
    @PostMapping("datumConfirm")
    public BaseResponse datumConfirm(@RequestBody SingleId<String> modelId, @SessionAttribute("user") UserSession user) {
        DatumConfirmDTO datumConfirmDTO = new DatumConfirmDTO();
        datumConfirmDTO.setModelId(modelId.getId());
        datumConfirmDTO.setUserId(user.getUserId());
        datumConfirmDTO.setUserName(user.getUserName());
        datumConfirmDTO.setOrgId(user.getOrgId());
        return vehicleModelApplyService.datumConfirm(datumConfirmDTO);
    }
    @Deprecated
    @ApiOperation(httpMethod = "POST", value = "检测中心资料退回 (经信委资料退回)")
    @PostMapping("datumDeny")
    public BaseResponse datumDeny(@RequestBody SingleId<String> modelId, @SessionAttribute("user") UserSession user) {
        return vehicleModelApplyService.datumDeny(modelId.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
    }

    @Deprecated
    @ApiOperation(httpMethod = "POST", value = "检测中心资料回滚")
    @PostMapping("datumRollback")
    public BaseResponse datumRollback(@RequestBody SingleId<String> modelId, @SessionAttribute("user") UserSession user){
        return vehicleModelApplyService.datumRollback(modelId.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
    }


    @ApiOperation(httpMethod = "POST", value = "车型评估确认")
    @PostMapping("reportConfirm")
    public BaseResponse reportConfirm(@RequestBody SingleId<String> modelId, @SessionAttribute("user") UserSession user) {
        return vehicleModelApplyService.reportConfirm(modelId.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
    }

    @ApiOperation(httpMethod = "POST", value = "允许上牌")
    @PostMapping("allowGetLicense")
    public BaseResponse allowGetLicense(@RequestBody SingleId<String> modelId, @SessionAttribute("user") UserSession user) {
        return vehicleModelApplyService.allowGetLicense(modelId.getId(), user.getOrgId(), user.getUserId(), user.getUserName());
    }

    @ApiOperation(httpMethod = "POST", value = "下载车型参数表")
    @PostMapping("downloadVehicleParam")
    public HttpEntity<byte[]> downloadVehicleParam(@RequestBody DownloadParamVO downloadParamVO) throws UnsupportedEncodingException {
        AssertUtil.notEmpty(downloadParamVO.getIds(), "至少选择一个车型");
        String fileName = "车型信息参数_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (Integer.valueOf(1).equals(downloadParamVO.getType())) {
            // 检测中心
            AssertUtil.isTrue(downloadParamVO.getIds().size() == 1, "只能选择一个车型");
            fileName = fileName + "_" + downloadParamVO.getIds().get(0);
        }
        byte[] bytes = vehicleModelApplyService.downloadVehicleParam(downloadParamVO.getIds(), downloadParamVO.getType());
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment;filename=" + System.currentTimeMillis() + ".pdf");
        headers.add("Content-Type", "application/pdf;charset=utf-8");
        headers.add("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8.displayName()));
        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }


    @ApiOperation(value = "经信委车型信息导出")
    public HttpEntity<byte[]> exportVehicleModel(@RequestBody SearchVehicleModelApplyVO searchVO) {
        SearchVehicleModelApplyDTO searchVehicleModelApplyDTO = ConvertUtil.convert(searchVO, SearchVehicleModelApplyDTO.class);
        searchVehicleModelApplyDTO.setOffset(CommonUtil.getOffset(searchVO.getPageNum(), searchVO.getPageSize()));
        searchVehicleModelApplyDTO.setLimit(searchVO.getPageSize());
        byte[] bytes = vehicleModelApplyService.exportVehicleModel(searchVehicleModelApplyDTO);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "attachment;filename=" + System.currentTimeMillis() + ".pdf");
        headers.add("Content-Type", "application/pdf;charset=utf-8");
        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }

    @ApiOperation(httpMethod = "POST", value = "获取车型上传文件")
    @PostMapping("getModelFile")
    @Authorize(accessAuthorize = false)
    public FileInfoBO getModelFile(@RequestBody GetVehicleModelFileVO getVehicleModelFileVO) {
        GetVehicleModelFileDTO getVehicleModelFileDTO = new GetVehicleModelFileDTO();
        getVehicleModelFileDTO.setType(getVehicleModelFileVO.getType());
        getVehicleModelFileDTO.setModelId(getVehicleModelFileVO.getModelId());
        return vehicleModelApplyService.getFile(getVehicleModelFileDTO);
    }

    @ApiOperation(httpMethod = "POST", value = "查看车型操作日志")
    @PostMapping("getModelOperateLog")
    public PageInfoBO<VehicleModelOperateLog> getModelOperateLog(@RequestBody SingleId<String> modelId) {
        AssertUtil.hasText(modelId.getId(), "车型编号不能为空");
        return vehicleModelApplyService.getModelOperateLog(modelId.getId());
    }


    @Authorize(loginAuthorize = false)
    @ApiOperation(httpMethod = "POST", value = "根据车型下载车型报告")
    @PostMapping("downloadVehicleModelFiles")
    public FileResponse downloadVehicleModelFiles(@RequestBody MultiId<String> modelIds){
        return vehicleModelApplyService.downloadVehicleModelFiles(modelIds.getIds());
    }

}
