package com.extracme.nevmp.schedule;

import static com.extracme.nevmp.mapper.OwnerInfoDynamicSqlSupport.ownerInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThanWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotNull;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;
import static org.mybatis.dynamic.sql.SqlBuilder.or;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.nevmp.enums.OwnerStatusEnum;
import com.extracme.nevmp.mapper.extend.OwnerInfoExtendMapper;
import com.extracme.nevmp.model.OwnerChargeInfo;
import com.extracme.nevmp.model.OwnerInfo;
import com.extracme.nevmp.model.VehicleInfo;
import com.extracme.nevmp.service.async.AsyncService;
import com.extracme.nevmp.service.owner.OwnerService;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.service.traffic.TrafficPushService;
import com.extracme.nevmp.service.vehicle.VehicleService;
import com.extracme.nevmp.utils.DateUtil;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/12/10
 */
@RestController
@RequestMapping("schedule")
@Slf4j
public class ScheduleController {

    // 时间相关常量
    private static final long ONE_HOUR_MILLIS = TimeUnit.HOURS.toMillis(1);
    private static final int DAYS_OFFSET_FOR_PREVIOUS_DAY = -1;

    // 数据库查询相关常量
    private static final int MAX_BATCH_ITERATIONS = 1000;
    private static final int BATCH_SIZE = 100;
    private static final int VALID_FLAG = 1;
    private static final int ENABLED_STATUS = 1;
    private static final int DISABLED_STAKE_ID = 0;
    private static final long INITIAL_LAST_ID = 0L;

    // 日志消息模板常量
    private static final String TASK_SCHEDULED_LOG_TEMPLATE = "任务：{} 被调度。";
    private static final String TASK_START_LOG_TEMPLATE = "开始定时处理：{}";
    private static final String TASK_END_LOG_TEMPLATE = "结束定时处理：{}";
    private static final String HEALTH_CHECK_MESSAGE = "定时任务健康检查";
    private static final String AUTO_REVIEW_QUALIFICATION_MESSAGE = "各部委已审核完成数据自动审核";
    private static final String EXPIRE_RECONSIDER_MESSAGE = "处理过期未复核办件";
    private static final String OMIT_QUALIFICATION_APPLY_MESSAGE = "一网通办遗漏的购车资质办件任务";
    private static final String OMIT_VEHICLE_CONFIRM_MESSAGE = "一网通办遗漏的车辆信息确认办件任务";
    private static final String OMIT_VEHICLE_REGISTRATION_MESSAGE = "一网通办遗漏的上牌进度查询办件任务";
    private static final String OUTER_DRIVER_QUERY_START_MESSAGE = "开始查询外地驾照名下有无新能源车";
    private static final String OUTER_DRIVER_QUERY_END_MESSAGE = "结束查询外地驾照名下有无新能源车";
    private static final String PENDING_QUALIFICATION_RETRY_MESSAGE = "各部委发送失败请求进行重试";
    private static final String VEHICLE_REGISTRATION_PROGRESS_MESSAGE = "用户上牌进度校验";
    private static final String TRAFFIC_PUSH_DATA_MESSAGE = "交委推送数据";
    private static final String TRAFFIC_NON_COMMERCIAL_QUOTA_MESSAGE = "交委推送数据查非营运车额度";
    private static final String TRAFFIC_EXCHANGE_QUOTA_MESSAGE = "交委推送数据查以旧换新专用额度";
    private static final String TRAFFIC_PULL_NON_COMMERCIAL_MESSAGE = "交委拉取数据查非营运车额度";
    private static final String TRAFFIC_PULL_EXCHANGE_MESSAGE = "交委拉取数据查以旧换新额度";
    private static final String AUTO_PROCESS_OWNER_REVIEW_MESSAGE = "自动审核充电条件确认信息";
    private static final String AUTO_SYNC_VEHICLE_MODEL_TIME_MESSAGE = "自动同步车型首张确认凭证时间";
    private static final String RECOVERY_CHARGE_INFO_LOG_TEMPLATE = "开始纠正充电条件确认信息：{}";

    @Autowired
    private OwnerQualificationService ownerQualificationService;
    @Autowired
    private OwnerService ownerService;
    @Autowired
    private VehicleService vehicleService;
    @Autowired
    private OwnerInfoExtendMapper ownerInfoExtendMapper;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private TrafficPushService trafficPushService;
    @Autowired
    private TaskExecutor scheduleExecutor;

    /**
     * 执行定时任务的通用模板方法
     * @param taskName 任务名称，用于日志记录
     * @param taskLogic 具体的任务执行逻辑
     */
    private void executeScheduledTask(String taskName, Runnable taskLogic) {
        log.info(TASK_SCHEDULED_LOG_TEMPLATE, taskName);
        Runnable runnable = () -> {
            try {
                log.info(TASK_START_LOG_TEMPLATE, taskName);
                taskLogic.run();
                log.info(TASK_END_LOG_TEMPLATE, taskName);
            } catch (Exception e) {
                log.error("定时任务执行失败，任务名称：{}，错误信息：{}", taskName, e.getMessage(), e);
            }
        };
        scheduleExecutor.execute(runnable);
    }

    /**
     * 执行简单定时任务的通用模板方法（不包含开始结束日志）
     * @param taskName 任务名称，用于日志记录
     * @param taskLogic 具体的任务执行逻辑
     */
    private void executeSimpleScheduledTask(String taskName, Runnable taskLogic) {
        log.info(TASK_SCHEDULED_LOG_TEMPLATE, taskName);
        Runnable runnable = () -> {
            try {
                taskLogic.run();
            } catch (Exception e) {
                log.error("定时任务执行失败，任务名称：{}，错误信息：{}", taskName, e.getMessage(), e);
            }
        };
        scheduleExecutor.execute(runnable);
    }

    @GetMapping("test")
    public String test() {
        executeSimpleScheduledTask("test", () -> {
            // 模拟业务处理
            log.info(new Date() + HEALTH_CHECK_MESSAGE);
        });
        return "success";
    }

    @GetMapping("autoReviewOwnerQualification")
    public void autoReviewOwnerQualification() {
        executeScheduledTask(AUTO_REVIEW_QUALIFICATION_MESSAGE, () -> {
            ownerQualificationService.autoReviewOwnerQualification();
        });
    }

    @GetMapping("dealExpireReconsiderQualification")
    public void dealExpireReconsiderQualification() {
        executeScheduledTask(EXPIRE_RECONSIDER_MESSAGE, () -> {
            ownerQualificationService.dealExpireReconsiderQualification();
        });
    }

    @GetMapping("dealOmitQualificationApply")
    public void dealOmitQualificationApply() {
        executeScheduledTask(OMIT_QUALIFICATION_APPLY_MESSAGE, () -> {
            ownerQualificationService.dealOmitQualificationApply();
        });
    }

    /**
     * 1小时执行一次 - 处理一网通办遗漏的车辆信息确认办件任务
     */
    @GetMapping("dealOmitVehicleConfirmTask")
    public void dealOmitVehicleConfirmTask() {
        executeScheduledTask(OMIT_VEHICLE_CONFIRM_MESSAGE, () -> {
            // 1. 处理待预审的办件（由于办件库读写延迟导致的遗漏）
            processOmitVehicleConfirmApplications();

            // 2. 处理车企点击提交客户确认但办件未创建成功的数据
            processUnlinkedOwnerConfirmations();
        });
    }

    /**
     * 处理一网通办遗漏的车辆信息确认办件
     * 理论上事项2（车辆信息确认）在用户点击提交的同时会调用接口自动受理
     * 但由于办件库读写存在延迟，故需要定时任务轮询待预审的办件
     */
    private void processOmitVehicleConfirmApplications() {
        log.info("开始处理待预审的车辆信息确认办件");
        ownerService.dealOmitVehicleConfirmTask();
        log.info("完成处理待预审的车辆信息确认办件");
    }

    /**
     * 处理车企点击提交客户确认但办件未创建成功的数据
     * 由于车企操作数据是先生成办件，后再对充电条件确认信息进行绑定，故存在延迟
     * 此处只处理已提交1个小时的数据
     */
    private void processUnlinkedOwnerConfirmations() {
        log.info("开始处理未关联办件的充电条件确认信息");

        Date oneHourAgo = new Date(System.currentTimeMillis() - ONE_HOUR_MILLIS);
        List<OwnerInfo> unlinkedOwners = queryUnlinkedOwnerConfirmations(oneHourAgo);

        log.info("找到 {} 条需要纠正的充电条件确认信息", unlinkedOwners.size());

        for (OwnerInfo recoveryOwner : unlinkedOwners) {
            processUnlinkedOwnerConfirmation(recoveryOwner);
        }

        log.info("完成处理未关联办件的充电条件确认信息");
    }

    /**
     * 查询未关联办件的充电条件确认信息
     * @param oneHourAgo 一小时前的时间点
     * @return 未关联办件的车主信息列表
     */
    private List<OwnerInfo> queryUnlinkedOwnerConfirmations(Date oneHourAgo) {
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerInfo.allColumns())
                .from(ownerInfo)
                .where(ownerInfo.ownerStatus, isEqualTo(OwnerStatusEnum.OWNER_CONFIRM.getOwnerStatus()))
                .and(ownerInfo.applyNo, isNull())
                .and(ownerInfo.uapplyNo, isNotNull())
                .and(ownerInfo.applyToOwnerConfirmTime, isLessThanWhenPresent(oneHourAgo))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerInfoExtendMapper.selectMany(selectStatement);
    }

    /**
     * 处理单个未关联办件的充电条件确认信息
     * @param recoveryOwner 需要纠正的车主信息
     */
    private void processUnlinkedOwnerConfirmation(OwnerInfo recoveryOwner) {
        try {
            log.debug(RECOVERY_CHARGE_INFO_LOG_TEMPLATE, recoveryOwner.getId());

            // 查询关联的车辆信息
            VehicleInfo relationVehicle = vehicleService.queryOwnerRelationVehicleInfo(recoveryOwner.getOwnerId());
            // 查询关联的充电桩信息
            OwnerChargeInfo relationCharge = ownerService.getOwnerChargeInfo(recoveryOwner.getId());

            // 提交车主申请
            asyncService.submitOwnerApply(recoveryOwner, relationVehicle, relationCharge);

            log.debug("成功纠正充电条件确认信息，车主ID：{}", recoveryOwner.getId());
        } catch (Exception e) {
            log.error("纠正充电条件确认信息失败，车主ID：{}，错误信息：{}", recoveryOwner.getId(), e.getMessage(), e);
        }
    }

    /**
     * 1天调用一次 - 处理一网通办遗漏的上牌进度查询办件任务
     */
    @GetMapping("dealOmitVehicleRegistration")
    public void dealOmitVehicleRegistration(){
        executeScheduledTask(OMIT_VEHICLE_REGISTRATION_MESSAGE, () -> {
            // 1. 处理未受理的车辆上牌进度办件
            processOmitVehicleRegistrationApplications();

            // 2. 处理已审核通过但事项3未生成的数据
            processApprovedButUnlinkedVehicleApplications();
        });
    }

    /**
     * 处理一网通办遗漏的车辆上牌进度查询办件
     * 理论上事项3（车辆上牌进度办件）不存在待预审（未受理）的情况，一般是由于创建时请求失败造成
     * TODO: 由于已申报（未提交）的办件无法通过接口查询，故之后需要对此处进行详细优化
     */
    private void processOmitVehicleRegistrationApplications() {
        log.info("开始处理未受理的车辆上牌进度办件");
        ownerService.dealOmitVehicleRegistration();
        log.info("完成处理未受理的车辆上牌进度办件");
    }

    /**
     * 处理已审核通过但事项3（车辆上牌进度办件）未生成的数据
     * 由于办件审批存在延迟，故这边只处理已审批一个小时且状态未同步的数据
     */
    private void processApprovedButUnlinkedVehicleApplications() {
        log.info("开始处理已审核通过但未生成车辆上牌进度办件的数据");

        Date oneHourAgo = new Date(System.currentTimeMillis() - ONE_HOUR_MILLIS);
        List<OwnerInfo> approvedOwners = queryApprovedButUnlinkedVehicleApplications(oneHourAgo);

        log.info("找到 {} 条需要生成车辆上牌进度办件的数据", approvedOwners.size());

        for (OwnerInfo recoveryOwner : approvedOwners) {
            processApprovedOwnerApplication(recoveryOwner);
        }

        log.info("完成处理已审核通过但未生成车辆上牌进度办件的数据");
    }

    /**
     * 查询已审核通过但事项3未生成的数据
     * @param oneHourAgo 一小时前的时间点
     * @return 已审核通过但未生成车辆上牌进度办件的车主信息列表
     */
    private List<OwnerInfo> queryApprovedButUnlinkedVehicleApplications(Date oneHourAgo) {
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerInfo.allColumns())
                .from(ownerInfo)
                .where(ownerInfo.uapplyNo, isNotNull())
                .and(ownerInfo.ownerStatus, isEqualTo(OwnerStatusEnum.APPROVE.getOwnerStatus()))
                .and(ownerInfo.applyNo, isNotNull())
                .and(ownerInfo.vehicleApplyNo, isNull())
                .and(ownerInfo.chargeAuditTime, isLessThanWhenPresent(oneHourAgo))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerInfoExtendMapper.selectMany(selectStatement);
    }

    /**
     * 处理单个已审核通过的车主申请，生成车辆上牌进度办件
     * @param recoveryOwner 需要处理的车主信息
     */
    private void processApprovedOwnerApplication(OwnerInfo recoveryOwner) {
        try {
            log.debug("开始为已审核通过的申请生成车辆上牌进度办件，车主ID：{}", recoveryOwner.getId());
            asyncService.approveOwnerApply(recoveryOwner.getApplyNo(), recoveryOwner);
            log.debug("成功生成车辆上牌进度办件，车主ID：{}", recoveryOwner.getId());
        } catch (Exception e) {
            log.error("生成车辆上牌进度办件失败，车主ID：{}，错误信息：{}", recoveryOwner.getId(), e.getMessage(), e);
        }
    }



    @GetMapping("queryOuterDriverHasNewEnergy")
    public void queryOuterDriverHasNewEnergy() {
        executeSimpleScheduledTask("queryOuterDriverHasNewEnergy", () -> {
            log.info(OUTER_DRIVER_QUERY_START_MESSAGE);
            ownerQualificationService.queryOuterDriverHasNewEnergy();
            log.info(OUTER_DRIVER_QUERY_END_MESSAGE);
        });
    }

    @GetMapping("dealPendingQualificationReview")
    public void dealPendingQualificationReview() {
        executeScheduledTask(PENDING_QUALIFICATION_RETRY_MESSAGE, () -> {
            ownerQualificationService.dealPendingQualificationReview();
        });
    }



    @GetMapping("syncVehicleRegistrationProgress")
    public void syncVehicleRegistrationProgress() {
        executeScheduledTask(VEHICLE_REGISTRATION_PROGRESS_MESSAGE, () -> {
            syncVehicleRegistrationProgressInBatches();
        });
    }

    /**
     * 分批同步车辆上牌进度
     * 查询所有审核通过且未同步牌照的数据（且有办件3的、审核通过的、有效的）
     */
    private void syncVehicleRegistrationProgressInBatches() {
        Long lastId = INITIAL_LAST_ID;
        int processedBatches = 0;

        for (int i = 0; i < MAX_BATCH_ITERATIONS; i++) {
            List<OwnerInfo> ownerInfoList = queryVehicleRegistrationProgressBatch(lastId);

            if (ownerInfoList.isEmpty()) {
                log.info("已处理完所有需要同步上牌进度的数据，共处理 {} 批次", processedBatches);
                break;
            }

            lastId = processBatchVehicleRegistrationProgress(ownerInfoList);
            processedBatches++;

            log.debug("完成第 {} 批次处理，本批次处理 {} 条记录，当前lastId：{}",
                     processedBatches, ownerInfoList.size(), lastId);
        }

        if (processedBatches >= MAX_BATCH_ITERATIONS) {
            log.warn("达到最大批次处理限制 {}，可能还有未处理的数据", MAX_BATCH_ITERATIONS);
        }
    }

    /**
     * 查询需要同步车辆上牌进度的数据批次
     * @param lastId 上次处理的最大ID
     * @return 需要处理的车主信息列表
     */
    private List<OwnerInfo> queryVehicleRegistrationProgressBatch(Long lastId) {
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerInfo.id, ownerInfo.vin, ownerInfo.vehicleApplyNo)
                .from(ownerInfo)
                .where()
                .and(ownerInfo.vehicleApplyNo, isNotNull())
                .and(ownerInfo.vehicleApplyNo, isNotEqualTo(""))
                .and(ownerInfo.ownerStatus, isEqualTo(OwnerStatusEnum.APPROVE.getOwnerStatus()))
                .and(ownerInfo.vehicleNo, isNull(), or(ownerInfo.vehicleNo, isEqualTo("")))
                .and(ownerInfo.stakeId, isNull(), or(ownerInfo.stakeId, isEqualTo(DISABLED_STAKE_ID)))
                .and(ownerInfo.vin, isNotNull())
                .and(ownerInfo.vin, isNotEqualTo(""))
                .and(ownerInfo.flag, isEqualTo(VALID_FLAG))
                .and(ownerInfo.disable, isEqualTo(ENABLED_STATUS))
                .and(ownerInfo.id, isGreaterThanWhenPresent(lastId))
                .limit(BATCH_SIZE)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerInfoExtendMapper.selectMany(selectStatement);
    }

    /**
     * 处理一批车辆上牌进度同步
     * @param ownerInfoList 需要处理的车主信息列表
     * @return 本批次处理的最大ID
     */
    private Long processBatchVehicleRegistrationProgress(List<OwnerInfo> ownerInfoList) {
        Long maxId = INITIAL_LAST_ID;

        for (OwnerInfo checkOwnerInfo : ownerInfoList) {
            try {
                asyncService.syncVehicleRegistrationProgress(checkOwnerInfo);
                maxId = checkOwnerInfo.getId();
                log.debug("成功同步车辆上牌进度，车主ID：{}，VIN：{}",
                         checkOwnerInfo.getId(), checkOwnerInfo.getVin());
            } catch (Exception e) {
                log.error("同步车辆上牌进度失败，车主ID：{}，VIN：{}，错误信息：{}",
                         checkOwnerInfo.getId(), checkOwnerInfo.getVin(), e.getMessage(), e);
                maxId = checkOwnerInfo.getId(); // 即使失败也要更新ID，避免重复处理
            }
        }

        return maxId;
    }



    @GetMapping("trafficPushData")
    public void trafficPushData() {
        executeScheduledTask(TRAFFIC_PUSH_DATA_MESSAGE, () -> {
            trafficPushService.pushData();
        });
    }

    @GetMapping("trafficCommitteePush")
    public void trafficCommitteePush() {
        executeSimpleScheduledTask("trafficCommitteePush", () -> {
            // 推送非营运车额度数据
            log.info(TASK_START_LOG_TEMPLATE, TRAFFIC_NON_COMMERCIAL_QUOTA_MESSAGE);
            trafficPushService.trafficCommitteePush();
            log.info(TASK_END_LOG_TEMPLATE, TRAFFIC_NON_COMMERCIAL_QUOTA_MESSAGE);

            // 推送以旧换新专用额度数据
            log.info(TASK_START_LOG_TEMPLATE, TRAFFIC_EXCHANGE_QUOTA_MESSAGE);
            trafficPushService.trafficCommitteeExchangePush();
            log.info(TASK_END_LOG_TEMPLATE, TRAFFIC_EXCHANGE_QUOTA_MESSAGE);
        });
    }

    @GetMapping("trafficCommitteePull")
    public void trafficCommitteePull() {
        executeSimpleScheduledTask("trafficCommitteePull", () -> {
            // 拉取前一天的额度数据
            String previousDayDateStr = getPreviousDayDateString();

            // 拉取非营运车额度数据
            pullNonCommercialQuotaData(previousDayDateStr);

            // 拉取以旧换新额度数据
            pullExchangeQuotaData(previousDayDateStr);
        });
    }

    /**
     * 获取前一天的日期字符串
     * @return 前一天的日期字符串
     */
    private String getPreviousDayDateString() {
        return DateUtil.format(DateUtil.addDays(new Date(), DAYS_OFFSET_FOR_PREVIOUS_DAY), DateUtil.DATE_TYPE3);
    }

    /**
     * 拉取非营运车额度数据
     * @param dateStr 指定日期字符串
     */
    private void pullNonCommercialQuotaData(String dateStr) {
        log.info(TASK_START_LOG_TEMPLATE, TRAFFIC_PULL_NON_COMMERCIAL_MESSAGE);
        trafficPushService.trafficCommitteePull();
        trafficPushService.trafficCommitteePull(dateStr);
        log.info(TASK_END_LOG_TEMPLATE, TRAFFIC_PULL_NON_COMMERCIAL_MESSAGE);
    }

    /**
     * 拉取以旧换新额度数据
     * @param dateStr 指定日期字符串
     */
    private void pullExchangeQuotaData(String dateStr) {
        log.info(TASK_START_LOG_TEMPLATE, TRAFFIC_PULL_EXCHANGE_MESSAGE);
        trafficPushService.trafficCommitteeExchangePull();
        trafficPushService.trafficCommitteeExchangePull(dateStr);
        log.info(TASK_END_LOG_TEMPLATE, TRAFFIC_PULL_EXCHANGE_MESSAGE);
    }

    @GetMapping("trafficCommitteePullWithDate/{date}")
    public void trafficCommitteePullWithDate(@PathVariable("date") String date) {
        executeSimpleScheduledTask("trafficCommitteePullWithDate", () -> {
            trafficPushService.trafficCommitteePull(date);
        });
    }

    @GetMapping("autoProcessOwnerReview")
    @ApiOperation(value = "自动审核充电条件确认信息", httpMethod = "GET")
    public void autoProcessOwnerReview(){
        executeScheduledTask(AUTO_PROCESS_OWNER_REVIEW_MESSAGE, () -> {
            ownerService.autoProcessOwnerReview();
        });
    }

    @GetMapping("autoSyncVehicleModelFirstRegTime")
    @ApiOperation(value = "自动同步车型首张确认凭证时间", httpMethod = "GET")
    public void autoSyncVehicleModelFirstRegTime(){
        executeScheduledTask(AUTO_SYNC_VEHICLE_MODEL_TIME_MESSAGE, () -> {
            ownerService.autoSyncVehicleModelFirstRegTime();
        });
    }
}
