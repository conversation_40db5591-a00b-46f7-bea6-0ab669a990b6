package com.extracme.nevmp.service.center;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.extracme.nevmp.utils.RestTemplateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 */

@Slf4j
@DisplayName("service center test")
@SpringBootTest(webEnvironment= SpringBootTest.WebEnvironment.NONE)
public class ServiceCenterTest {

    @Test
    public void test(){

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<>();
        params.put("name","蒋长瑜");
        params.put("idCard","310115199306110418");

        System.out.println(params);
        String response = RestTemplateUtil.post("26f64785-6009-49ee-9533-64c5e3418e2e", header, params, String.class);
        System.out.println(response);
    }
}
