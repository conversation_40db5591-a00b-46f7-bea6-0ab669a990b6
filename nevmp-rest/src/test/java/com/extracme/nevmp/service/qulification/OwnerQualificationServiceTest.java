package com.extracme.nevmp.service.qulification;

import static com.extracme.nevmp.mapper.OwnerQualificationDynamicSqlSupport.ownerQualification;
import static org.junit.Assert.assertTrue;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;

import org.junit.After;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvFileSource;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.event.annotation.AfterTestMethod;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.extracme.nevmp.auth.UserSession;
import com.extracme.nevmp.dto.gov.traffic.OwnerVehicleInfoDTO;
import com.extracme.nevmp.dto.owner.DriverLicenseDTO;
import com.extracme.nevmp.dto.owner.QueryOwnerSocialDetailDTO;
import com.extracme.nevmp.dto.owner.QueryOwnerSocialDetailResponse;
import com.extracme.nevmp.dto.qualification.owner.OwnerQualificationDetailDTO;
import com.extracme.nevmp.dto.qualification.owner.SaveBusinessOwnerQualificationDTO;
import com.extracme.nevmp.dto.qualification.owner.SaveMilitaryQualificationInfoDTO;
import com.extracme.nevmp.dto.qualification.owner.SavePrivateOwnerQualificationDTO;
import com.extracme.nevmp.enums.AuthTypeEnum;
import com.extracme.nevmp.enums.HouseholdRegistrationEnum;
import com.extracme.nevmp.enums.OwnerQualificationStatusEnum;
import com.extracme.nevmp.mapper.OwnerQualificationMapper;
import com.extracme.nevmp.mapper.OwnerQualificationReviewDetailMapper;
import com.extracme.nevmp.model.OwnerQualification;
import com.extracme.nevmp.model.OwnerQualificationReviewDetail;
import com.extracme.nevmp.model.OwnerTrafficQualification;
import com.extracme.nevmp.service.UserOperateLogService;
import com.extracme.nevmp.service.async.AsyncService;
import com.extracme.nevmp.service.library.LibraryService;
import com.extracme.nevmp.service.qualification.CreditService;
import com.extracme.nevmp.service.qualification.DriverService;
import com.extracme.nevmp.service.qualification.HouseholdRegistrationService;
import com.extracme.nevmp.service.qualification.NewSocialService;
import com.extracme.nevmp.service.qualification.OwnerQualificationService;
import com.extracme.nevmp.service.qualification.SocialService;
import com.extracme.nevmp.service.qualification.impl.DriverServiceImpl;
import com.extracme.nevmp.utils.DateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 */
@Slf4j
@DisplayName("owner qualification service test")
@SpringBootTest(webEnvironment= SpringBootTest.WebEnvironment.NONE)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class OwnerQualificationServiceTest {

    @Autowired
    private OwnerQualificationService ownerQualificationService;

    @Autowired
    private SocialService socialService;

    @Autowired
    private NewSocialService newSocialService;

    @Autowired
    private CreditService creditService;

    @Autowired
    private DriverService driverService;

    @Autowired
    private LibraryService libraryService;

    @Autowired
    private OwnerQualificationMapper ownerQualificationMapper;

    @Autowired
    private OwnerQualificationReviewDetailMapper ownerQualificationReviewDetailMapper;

    @Autowired
    private UserOperateLogService userOperateLogService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private HouseholdRegistrationService householdRegistrationService;

    private static UserSession userSession;

    private static OwnerQualification privateOwnerQualification;

    private static OwnerQualification businessOwnerQualification;

    private static OwnerTrafficQualification ownerTrafficQualification;

    private static OwnerQualification fakeOwnerQualification;

    private static OwnerTrafficQualification fakeOwnerTrafficQualification;




    @BeforeAll
    public static void init(){
        System.out.println("初始化数据");
        userSession = new UserSession();
        userSession.setUserId("-1");
        userSession.setUserName("unit test");
        userSession.setOrgId("09b5b3c6-f00b-4d6c-b1b8-ebb4968e267b");
        userSession.setOrgName("比亚迪汽车有限公司");
    }

    @Test
    public void test(){
        List<OwnerVehicleInfoDTO> ownerVehicleInfoList = driverService.queryDriverCurrentVehicleList("130202198403210088");
        System.out.println(JSON.toJSONString(ownerVehicleInfoList));
        //名下是否存在非新能源车辆 或采用大牌额度的新能源车辆
        if(ownerVehicleInfoList.stream().anyMatch(ownerVehicleInfo -> (!ownerVehicleInfo.isNewEnergy() || ownerVehicleInfo.getCarQuota() == 2))){
            System.out.println("通过");
        }else{
            System.out.println("名下暂未查询到有效的大牌额度车辆信息");
        }
    }

    @Test
    @Order(0)
    @DisplayName("初始化数据")
    public void setUp(){
        //清空测试记录
//        ownerQualificationMapper.delete(SqlBuilder.deleteFrom(OwnerQualificationDynamicSqlSupport.ownerQualification).build().render(RenderingStrategies.MYBATIS3));
//        ownerQualificationReviewDetailMapper.delete(SqlBuilder.deleteFrom(OwnerQualificationReviewDetailDynamicSqlSupport.ownerQualificationReviewDetail).build().render(RenderingStrategies.MYBATIS3));
    }


    @Test
    @Order(1)
    @DisplayName("创建意向用户测试数据")
    public void testCreateSimpleOwnerQualification(){
        OwnerQualification privateOwnerQualification = new OwnerQualification();
        privateOwnerQualification.setName("孙克遥");
        privateOwnerQualification.setAuthType(AuthTypeEnum.IDENTIFICATION_CARD.getType());
        privateOwnerQualification.setAuthId("320602199011052537");
        privateOwnerQualification.setBirthDay(DateUtil.parse("1993-06-11", DateUtil.DATE_TYPE3));
        privateOwnerQualification.setHouseholdRegistrationType(HouseholdRegistrationEnum.SHANGHAI.getValue());
        privateOwnerQualification.setProperty(1);
        ownerQualificationMapper.insertSelective(privateOwnerQualification);
        assertTrue(ownerQualificationMapper.selectByPrimaryKey(privateOwnerQualification.getId()).isPresent());
        OwnerQualificationServiceTest.privateOwnerQualification = privateOwnerQualification;

        OwnerTrafficQualification ownerTrafficQualification = new OwnerTrafficQualification();
        ownerTrafficQualification.setDriverLicenseCode("310115199306110418");
        ownerTrafficQualification.setDriverLicenseIssuingPlace("上海市");
        ownerTrafficQualification.setDriverLicenseIssuingOrganization("上海浦东公安局");
        OwnerQualificationServiceTest.ownerTrafficQualification = ownerTrafficQualification;

        OwnerQualification businessOwnerQualification = new OwnerQualification();
        businessOwnerQualification.setName("上海万炼实业有限公司");
        businessOwnerQualification.setAuthType(AuthTypeEnum.CERTIFICATE_FOR_UNIFORM_SOCIAL_CREDIT_CODE.getType());
        businessOwnerQualification.setAuthId("91310113MA1GKBNL0J");
        businessOwnerQualification.setProperty(2);
        ownerQualificationMapper.insertSelective(businessOwnerQualification);
        assertTrue(ownerQualificationMapper.selectByPrimaryKey(businessOwnerQualification.getId()).isPresent());
        OwnerQualificationServiceTest.businessOwnerQualification = businessOwnerQualification;

        OwnerQualification fakeOwnerQualification = new OwnerQualification();
        fakeOwnerQualification.setName("蒋长瑜2");
        fakeOwnerQualification.setAuthType(AuthTypeEnum.IDENTIFICATION_CARD.getType());
        fakeOwnerQualification.setAuthId("310114199306110418");
        fakeOwnerQualification.setBirthDay(DateUtil.parse("1993-06-11", DateUtil.DATE_TYPE3));
        fakeOwnerQualification.setHouseholdRegistrationType(HouseholdRegistrationEnum.SHANGHAI.getValue());
        fakeOwnerQualification.setProperty(1);
        ownerQualificationMapper.insertSelective(fakeOwnerQualification);
        assertTrue(ownerQualificationMapper.selectByPrimaryKey(fakeOwnerQualification.getId()).isPresent());
        OwnerQualificationServiceTest.fakeOwnerQualification = fakeOwnerQualification;

    }

    @Test
    public void getOwnerQualificationDetailTest(){
        OwnerQualificationDetailDTO ownerQualificationDetail = ownerQualificationService.getOwnerQualificationDetail(589L);
        System.out.println(JSON.toJSONString(ownerQualificationDetail));
    }

    @Test
    @DisplayName("查询社保")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testNewSocialQualification(){
//        newSocialService.review(-1L, "上海帮楚自动化科技有限公司"
//                , AuthTypeEnum.CERTIFICATE_FOR_UNIFORM_SOCIAL_CREDIT_CODE.getType(), "91310112086216389G");

//
//        newSocialService.review(-1L, "孙均"
//                , AuthTypeEnum.IDENTIFICATION_CARD.getType(), "321282198404263235");

//        newSocialService.review(-1L, "刘巾葛格"
//                , AuthTypeEnum.IDENTIFICATION_CARD.getType(), "230822199308106422");

//        newSocialService.review(-1L, "蒋长瑜", AuthTypeEnum.IDENTIFICATION_CARD.getType(), "310115199306110418");

//        newSocialService.review(-1L, "王苏熠", AuthTypeEnum.IDENTIFICATION_CARD.getType(), "320113199202076445");

//        newSocialService.reviewSocial2025( "蒋长瑜", AuthTypeEnum.IDENTIFICATION_CARD.getType(), "310115199306110418",36, 48);

//        newSocialService.reviewSocial2025( "余伟", AuthTypeEnum.IDENTIFICATION_CARD.getType(), "321281199203261717",36, 48);

//        newSocialService.reviewSocial("蒋长瑜", AuthTypeEnum.IDENTIFICATION_CARD.getType(), "310115199306110418",36);





        QueryOwnerSocialDetailDTO queryOwnerSocialDetailDTO = new QueryOwnerSocialDetailDTO();
        queryOwnerSocialDetailDTO.setName("王文高");
        queryOwnerSocialDetailDTO.setAuthType(AuthTypeEnum.IDENTIFICATION_CARD.getType());
        queryOwnerSocialDetailDTO.setAuthId("321025197205118614");
        QueryOwnerSocialDetailResponse response = ownerQualificationService.queryOwnerSocialDetail(queryOwnerSocialDetailDTO);
        System.out.println(JSON.toJSONString(response));


        queryOwnerSocialDetailDTO = new QueryOwnerSocialDetailDTO();
        queryOwnerSocialDetailDTO.setName("王文高");
        queryOwnerSocialDetailDTO.setAuthType(AuthTypeEnum.IDENTIFICATION_CARD.getType());
        queryOwnerSocialDetailDTO.setAuthId("321025197205118614");
        queryOwnerSocialDetailDTO.setStartTime(DateUtil.parse("2023-01-01 00:00:00", DateUtil.DATE_TYPE1));
        queryOwnerSocialDetailDTO.setEndTime(DateUtil.parse("2024-01-01 00:00:00", DateUtil.DATE_TYPE1));

        response = ownerQualificationService.queryOwnerSocialDetail(queryOwnerSocialDetailDTO);
        System.out.println(JSON.toJSONString(response));


//        NewSocialServiceImpl.SocialResponse response = newSocialService.reviewSocialFromSocialAPI2025("周雪娟", AuthTypeEnum.IDENTIFICATION_CARD.getType()
//                , "320211199003161922", "202301", "202312");
//        System.out.println(JSON.toJSONString(response));


//        NewSocialServiceImpl.SocialResponse response1 = newSocialService.reviewSocialFromSocialAPI("周雪娟", AuthTypeEnum.IDENTIFICATION_CARD.getType()
//                , "320211199003161922", 6);
//        System.out.println(JSON.toJSONString(response1));

    }

    @Test
    @DisplayName("查询用户户籍信息")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testHouseholdRegistrationQualification(){
        boolean result = householdRegistrationService.isLocalHousehold("刘方德", "310103196912040457");
        System.out.println(result);
    }

    @Test
    @DisplayName("查询社保")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testSocialQualification(){
        socialService.review(privateOwnerQualification.getId(), privateOwnerQualification.getName()
                , privateOwnerQualification.getAuthType(), privateOwnerQualification.getAuthId());

        socialService.review(fakeOwnerQualification.getId(), fakeOwnerQualification.getName()
                , fakeOwnerQualification.getAuthType(), fakeOwnerQualification.getAuthId());
    }


    @Test
    @DisplayName("查询信用")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testCreditQualification(){
//        creditService.review(privateOwnerQualification.getId(), privateOwnerQualification.getProperty()
//                , privateOwnerQualification.getName(), privateOwnerQualification.getAuthId());
//
//        creditService.review(fakeOwnerQualification.getId(), fakeOwnerQualification.getProperty()
//                , fakeOwnerQualification.getName(), fakeOwnerQualification.getAuthId());

        creditService.review(-1L, 1, "肖杨", "******************");
    }

    @Test
    @DisplayName("查询公安")
    @AfterTestMethod(value = "testCreateSimpleTrafficQualification")
    public void testTrafficQualification(){


    }


    @Test
    @Disabled
    @DisplayName("保存军官信息")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testSaveMilitaryQualificationInfo(){
        SaveMilitaryQualificationInfoDTO saveMilitaryQualificationInfoDTO = new SaveMilitaryQualificationInfoDTO();
        saveMilitaryQualificationInfoDTO.setName("蒋长瑜");
        saveMilitaryQualificationInfoDTO.setAuthType(AuthTypeEnum.IDENTIFICATION_CARD.getType());
        saveMilitaryQualificationInfoDTO.setAuthId("310115199306110418");
        saveMilitaryQualificationInfoDTO.setBirthDay(DateUtil.parse("1993-06-11", DateUtil.DATE_TYPE3));
        saveMilitaryQualificationInfoDTO.setHouseholdRegistrationType(HouseholdRegistrationEnum.SHANGHAI.getValue());
        saveMilitaryQualificationInfoDTO.setDriverLicenseIssuingPlace("上海市");
        saveMilitaryQualificationInfoDTO.setDriverLicenseIssuingOrganization("上海市公安局");
        saveMilitaryQualificationInfoDTO.setDriverLicenseCode("310115199306110418");
        String militaryLicenseFile = "2020//08//10//02//ebb4968e267b_9c767f164a39_1597071883881_840.png";
        saveMilitaryQualificationInfoDTO.setFile(new ArrayList<String>(){{add(militaryLicenseFile);}});
        saveMilitaryQualificationInfoDTO.setCreatedUserId(userSession.getUserId());
        saveMilitaryQualificationInfoDTO.setCreatedUserName(userSession.getUserName());
        ownerQualificationService.saveMilitaryQualificationInfo(saveMilitaryQualificationInfoDTO);
    }

    @Test
    @DisplayName("保存私人意向用户资质")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testSavePrivateOwnerQualification(){
        SavePrivateOwnerQualificationDTO savePrivateOwnerQualificationDTO = new SavePrivateOwnerQualificationDTO();
        savePrivateOwnerQualificationDTO.setName(privateOwnerQualification.getName());
        savePrivateOwnerQualificationDTO.setAuthId(privateOwnerQualification.getAuthId());
        savePrivateOwnerQualificationDTO.setAuthType(privateOwnerQualification.getAuthType());
        savePrivateOwnerQualificationDTO.setHouseholdRegistrationType(privateOwnerQualification.getHouseholdRegistrationType());

        savePrivateOwnerQualificationDTO.setDriverFileNo(ownerTrafficQualification.getDriverFileNo());
        savePrivateOwnerQualificationDTO.setDriverLicenseCode(ownerTrafficQualification.getDriverLicenseCode());
        savePrivateOwnerQualificationDTO.setDriverLicenseIssuingOrganization(ownerTrafficQualification.getDriverLicenseIssuingOrganization());
        savePrivateOwnerQualificationDTO.setDriverLicenseIssuingPlace(ownerTrafficQualification.getDriverLicenseIssuingPlace());

        savePrivateOwnerQualificationDTO.setCreatedUserId(userSession.getUserId());
        savePrivateOwnerQualificationDTO.setCreatedUserName(userSession.getUserName());
        ownerQualificationService.savePrivateOwnerQualification(savePrivateOwnerQualificationDTO);
    }

    @Test
    @DisplayName("保存企业意向用户资质")
    @AfterTestMethod(value = "testCreateSimpleOwnerQualification")
    public void testSaveBusinessOwnerQualification(){
        SaveBusinessOwnerQualificationDTO saveBusinessOwnerQualificationDTO = new SaveBusinessOwnerQualificationDTO();
        saveBusinessOwnerQualificationDTO.setName(businessOwnerQualification.getName());
        saveBusinessOwnerQualificationDTO.setAuthId(businessOwnerQualification.getAuthId());
        saveBusinessOwnerQualificationDTO.setCreatedUserId(userSession.getUserId());
        saveBusinessOwnerQualificationDTO.setCreatedUserName(userSession.getUserName());
        ownerQualificationService.saveBusinessOwnerQualification(saveBusinessOwnerQualificationDTO);
    }

    @Test
    public void testDealOmitQualificationApply(){
        ownerQualificationService.dealOmitQualificationApply();
    }

    @Test
    public void testDealOmitQualificationApplyByApplyNo(){
        ownerQualificationService.dealQualificationApply("03W425720000062", null);
    }

    @Test
    public void testDealSupplementQualificationApply(){
        String applyNo = "03W4257200000C7";
        ownerQualificationService.dealSupplementQualificationApply(applyNo);
    }

    @Test
    public void testSpecialPending(){
        List<OwnerQualificationReviewDetail> ownerQualificationReviewDetails = driverService.querySpecialPendingReview();
        System.out.println(JSON.toJSONString(ownerQualificationReviewDetails));
    }

    @Transactional
    @Rollback
    @Test
    public void testReviewTrafficQualification(){
        Long ownerQualificationId = 1L;
        final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode("310115199306110418")
                .driverLicenseIssuingOrganization("上海市车管所")
                .driverLicenseIssuingPlace("上海市")
                .build();
        driverService.review(ownerQualificationId, driverLicense);
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/社保补查.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testSocialQuery(Long id, String name,String authId){
        newSocialService.review(id, name
                , AuthTypeEnum.CERTIFICATE_FOR_UNIFORM_SOCIAL_CREDIT_CODE.getType(), authId);
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/社保无记录.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testNoSocialResult(String name, String ownerType, String autType, String authId, String nationality
            , String result, String remark){
        System.out.println(name + "-" + authId);
        if(Objects.equals(autType, "身份证")){
            socialService.review(1L,name, AuthTypeEnum.IDENTIFICATION_CARD.getType(),authId);
        }else{
            socialService.review(1L,name, AuthTypeEnum.RESIDENCE_PERMIT.getType(),authId);
        }
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/社保不满一年.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testSocialDeny(String name, String ownerType, String autType, String authId, String nationality
            , String result, String remark){
        System.out.println(name + "-" + authId);
        if(Objects.equals(autType, "身份证")){
            socialService.review(1L,name, AuthTypeEnum.IDENTIFICATION_CARD.getType(),authId);
        }else{
            socialService.review(1L,name, AuthTypeEnum.RESIDENCE_PERMIT.getType(),authId);
        }
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/法人信用记录.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testBusinessCreditResult(String name, String authId){
        System.out.println(name + "-" + authId);
        creditService.review(1L,2,name,authId);
    }


    @ParameterizedTest
    @CsvFileSource(resources = "/私人信用无记录.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testPrivateNoCreditResult(String name, String authId){
        System.out.println(name + "-" + authId);
        creditService.review(1L,1,name,authId);

    }

    @ParameterizedTest
    @CsvFileSource(resources = "/私人信用不通过.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testPrivateCreditDeny(String name, String authId){
        System.out.println(name + "-" + authId);
        creditService.review(1L,1,name,authId);
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/驾照无记录.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testNoDriverResult(String name, String authType, String authId){
        System.out.println(name + "-" + authId);

        if("身份证".equals(authType)){
            final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                    .driverLicenseCode(authId)
                    .driverLicenseIssuingOrganization("上海市车管所")
                    .driverLicenseIssuingPlace("上海市")
                    .build();
            driverService.review(1L, driverLicense);
        }else{
            final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                    .driverFileNo(authId)
                    .driverLicenseIssuingOrganization("上海市车管所")
                    .driverLicenseIssuingPlace("上海市")
                    .build();
            driverService.review(1L, driverLicense);
        }

    }


    @ParameterizedTest
    @CsvFileSource(resources = "/违章大于五次.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testDriverIllegal(String name, String authType, String authId) throws InterruptedException {
        System.out.println(name + "-" + authId);
        Long ownerQualificationId = 1L;
        final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode(authId)
                .driverLicenseIssuingOrganization("上海市车管所")
                .driverLicenseIssuingPlace("上海市")
                .build();
        driverService.review(1L, driverLicense);
    }

    @ParameterizedTest
    @CsvFileSource(resources = "/名下有新能源汽车.csv",numLinesToSkip = 1, encoding = "GBK")
    public void testDriverHasVehicle(String name, String authType, String authId) throws InterruptedException {
        System.out.println(name + "-" + authId);
        Long ownerQualificationId = 1L;
        final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode(authId)
                .driverLicenseIssuingOrganization("上海市车管所")
                .driverLicenseIssuingPlace("上海市")
                .build();
        driverService.review(1L, driverLicense);
        Thread.sleep(5000);
    }

    @Test
    public void testDiver(){
//        final DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
//                .driverLicenseCode("51122619830816002X")
//                .driverLicenseIssuingOrganization("上海市车管所")
//                .driverLicenseIssuingPlace("上海市")
//                .build();
//        driverService.review(322883L, driverLicense);
        System.out.println(DriverServiceImpl.queryViolationCount("310114197706192613"));

    }


    public static void main(String[] args) {
//        String response = "[{\"SFZMHM\":\"320321198706083928\",\"XM\":\"蒋海婷\",\"DABH\":\"310045951628\",\"YXQZ\":\"2023-01-25 00:00:00\",\"ZT\":\"A\",\"SFZMMC\":\"A\"}]";
//
//        List<DriverLicenseInfo> driverLicenseInfoList = JsonUtil.readStr(response, new TypeReference<List<DriverLicenseInfo>>() {});
//        System.out.println(driverLicenseInfoList);


        int month = 31;
        Calendar instance = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
        //从2023年11月开始算起
        instance.set(Calendar.YEAR, 2023);
        instance.set(Calendar.MONTH, Calendar.NOVEMBER);
        instance.add(Calendar.DATE, -1);

        System.out.println(instance.get(Calendar.MONTH));

        instance.set(Calendar.DATE, 1);

        instance.add(Calendar.MONTH, -month +1);

        String yyyyMM = DateUtil.format(instance.getTime(), DateUtil.DATE_TYPE6);

        System.out.println(yyyyMM);

    }

    @After
    public void destroy(){

    }

    @Test
    public void query() {
        OwnerQualificationDetailDTO ownerQualificationDetailDTO = ownerQualificationService.queryOwnerQualificationDetail(1, "310115198405251019");
        System.out.println(JSON.toJSONString(ownerQualificationDetailDTO));
    }

    @Test
    public void queryItemsApplyList() {
//        List<String> itemCodes = new ArrayList<String>() {{
//            add(LibraryApplyItemEnum.OWNER_QUALIFICATION_APPLY.getItemCode());
//        }};
//        QueryItemsApplyListResponse queryItemsApplyListResponse = libraryService.queryItemsApplyList(itemCodes, "待预审", 100);
//        System.out.println(JSON.toJSONString(queryItemsApplyListResponse));

        ownerQualificationService.dealOmitQualificationApply();
    }

    @Test
    public void dealPendingQualificationReview(){

        ownerQualificationService.dealPendingQualificationReview();
    }



    @ParameterizedTest
    @CsvFileSource(resources = "/购车资质异常数据.csv",numLinesToSkip = 1, encoding = "GBK")
    public void denyAbnormalOwnerQualification(Long id){

        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(id);
        if(optionalOwnerQualification.isPresent()){
            String reason = "数据异常，请重新提交申请";
            Date now = new Date();
            UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                    .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.DENY.getStatus())
                    .set(ownerQualification.reviewTime).equalTo(now)
                    .set(ownerQualification.reason).equalTo(reason)
                    //注:此处需要将过期时间置空，来区分是允许补正的还是直接拒绝的
                    .set(ownerQualification.expireTime).equalToNull()
                    .set(ownerQualification.updatedTime).equalTo(now)
                    .set(ownerQualification.updatedUserId).equalTo("-1")
                    .set(ownerQualification.updatedUserName).equalTo("管理员")
                    .where(ownerQualification.id, isEqualTo(id))
//                    .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.RECONSIDERATION.getStatus()))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationMapper.update(updateStatement);

//            //保存操作日志
//            SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
//                    .userOperateTypeEnum(UserOperateTypeEnum.DENY_OWNER_QUALIFICATION)
//                    .reason(reason)
//                    .vehicleId(id)
//                    .operateUserId("-1")
//                    .operateUserName("管理员")
//                    .build();
//            userOperateLogService.saveOperateLog(saveOperateLogDTO);
//
//
//            //更新办件库信息
//            if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
//                asyncService.denyOwnerQualification(optionalOwnerQualification.get().getApplyNo(), reason);
//            }
        }



    }


}
