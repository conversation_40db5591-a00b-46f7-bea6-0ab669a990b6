package com.extracme.nevmp;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;

/**
 * <AUTHOR>
 * @date 2020/11/13
 */
@SpringBootTest
public class PdfTest {

    @Test
    public void testPdf() {
        OutputStream os = null;
        PdfStamper ps = null;
        PdfReader reader = null;
        try {
            os = new FileOutputStream(new File("D:\\tmpFile\\1.pdf"));
            // 2 读入pdf表单
            reader = new PdfReader(PdfTest.class.getResourceAsStream("/pdf/纯电动乘用车车型管理.pdf"));
            // 3 根据表单生成一个新的pdf
            ps = new PdfStamper(reader, os);
            // 4 获取pdf表单
            AcroFields form = ps.getAcroFields();
            // 5给表单添加中文字体 这里采用系统字体。不设置的话，中文可能无法显示
            // BaseFont bf = BaseFont.createFont("C:/Windows/Fonts/SIMYOU.TTF",
            // BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            // 方法二：使用iTextAsian.jar中的字体
            // BaseFont baseFont =
            // BaseFont.createFont("STSong-Light",BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);
            BaseFont bf = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", false);
            form.addSubstitutionFont(bf);
            // 6查询数据================================================
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("orgProductName", "111111");
            data.put("configId", "111111");
            // 7遍历data 给pdf表单表格赋值
            for (String key : data.keySet()) {
                form.setField(key, data.get(key).toString());
            }
            ps.setFormFlattening(true);
            System.out.println("===============PDF导出成功=============");
        } catch (Exception e) {
            System.out.println("===============PDF导出失败=============");
            e.printStackTrace();
        } finally {
            try {
                ps.close();
                reader.close();
                os.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}
