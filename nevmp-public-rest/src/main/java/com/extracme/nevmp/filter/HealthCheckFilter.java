package com.extracme.nevmp.filter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * <AUTHOR>
 * @Description 健康检查
 */
public class HealthCheckFilter extends OncePerRequestFilter {

    static final String HEALTH_CHECK_PATH = "/health";

    Logger logger = LoggerFactory.getLogger(HealthCheckFilter.class);


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (HEALTH_CHECK_PATH.equals(request.getRequestURI())) {
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().println("OK");
        } else {
            logger.debug("访问接口:" + request.getServletPath());
            filterChain.doFilter(request, response);
        }
    }
}
