package com.extracme.nevmp.service.vehicle;

import java.util.List;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.dto.common.FileInfoBO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.file.FileResponse;
import com.extracme.nevmp.dto.vehicle.model.CommitModelDTO;
import com.extracme.nevmp.dto.vehicle.model.DatumConfirmDTO;
import com.extracme.nevmp.dto.vehicle.model.GetVehicleModelFileDTO;
import com.extracme.nevmp.dto.vehicle.model.ModelRemarkDTO;
import com.extracme.nevmp.dto.vehicle.model.SearchVehicleModelApplyDTO;
import com.extracme.nevmp.dto.vehicle.model.UploadModelFileDTO;
import com.extracme.nevmp.model.VehicleModel2017;
import com.extracme.nevmp.model.VehicleModelOperateLog;

/**
 * <AUTHOR>
 * @date 2020/11/10
 */
public interface VehicleModelApplyService {

    /**
     * 新增车型
     * @param model
     * @return
     */
    BaseResponse addVehicleModel(VehicleModel2017 model);


    /**
     * 获取车型详情
     * @param id
     * @param orgId
     * @return
     */
    BaseResponse getVehicleModelDetail(String id, String orgId);

    /**
     * 更新车型
     * @param vehicleModel2017
     * @return
     */
    BaseResponse updateVehicleModel(VehicleModel2017 vehicleModel2017);

    /**
     * 删除车型
     * @return
     */
    BaseResponse deleteVehicleModel(String modelId, String orgId);

    //TODO 上传11111

    /**
     * 上传申请书
     * @return
     */
    BaseResponse uploadApplyDoc(UploadModelFileDTO uploadModelFileDTO);
    /**
     * 上传证明材料
     * @return
     */
    BaseResponse uploadConfirmDoc(UploadModelFileDTO uploadModelFileDTO);

    /**
     * 备注
     * @return
     * @param modelRemarkDTO
     */
    BaseResponse remark(ModelRemarkDTO modelRemarkDTO);

    /**
     * 车企提交
     * @param commitModelDTO
     * @return
     */
    BaseResponse commitOne(CommitModelDTO commitModelDTO);

    /**
     * 数据中心上传证明材料
     * @param uploadModelFileDTO
     * @return
     */
    BaseResponse uploadCertificate(UploadModelFileDTO uploadModelFileDTO);

    /**
     * 对接通过
     * @param id  车型ID
     * @param orgId
     * @param userId
     * @param userName
     * @return
     */
    BaseResponse accessApprove(String id, String orgId, String userId, String userName);

    /**
     * 对接失败
     * @param id
     * @param orgId
     * @param userId
     * @param userName
     * @return
     */
    BaseResponse accessDeny(String id, String orgId, String userId, String userName);

    /**
     * 查询车型
     * @param searchVehicleModelApplyDTO
     * @return
     */
    BaseResponse searchVehicleModel(SearchVehicleModelApplyDTO searchVehicleModelApplyDTO);

    /**
     * 查询待审批车型
     * @param searchVehicleModelApplyDTO
     * @return
     */
    BaseResponse approveVehicleModel(SearchVehicleModelApplyDTO searchVehicleModelApplyDTO);


    /**
     * 评审报告
     * @param uploadModelFileDTO
     * @return
     */
    BaseResponse uploadReviewReport(UploadModelFileDTO uploadModelFileDTO);

    /**
     * 质量抽查表
     * @param uploadModelFileDTO
     * @return
     */
    BaseResponse uploadCheckReport(UploadModelFileDTO uploadModelFileDTO);

    /**
     * 上传车型参数表
     * @param uploadModelFileDTO
     * @return
     */
    BaseResponse uploadParamReport(UploadModelFileDTO uploadModelFileDTO);

    /**
     * 检测中心资料确认
     * @param datumConfirmDTO
     * @return
     */
    BaseResponse datumConfirm(DatumConfirmDTO datumConfirmDTO);

    /**
     * 检测中心资料退回(经信委也可资料退回)
     * @param modelId
     * @return
     */
    BaseResponse datumDeny(String modelId, String orgId, String userId, String userName);

    /**
     * 经信委车型评估确认
     * @param modelId
     */
    BaseResponse reportConfirm(String modelId, String orgId, String userId, String userName);

    /**
     * 经信委允许车辆上牌
     * @param modelId
     */
    BaseResponse allowGetLicense(String modelId, String orgId, String userId, String userName);

    /**
     * 下载车型参数表
     * @param modelIds
     * @return
     */
    byte[] downloadVehicleParam(List<String> modelIds, Integer type);

    /**
     * 下载车型各类证明材料
     * @param getVehicleModelFileDTO
     * @return
     */
    FileInfoBO getFile(GetVehicleModelFileDTO getVehicleModelFileDTO);

    /**
     * 查看车型Id
     * @param modelId
     * @return
     */
    PageInfoBO<VehicleModelOperateLog> getModelOperateLog(String modelId);

    /**
     * 导出车型
     * @param searchVehicleModelApplyDTO
     * @return
     */
    byte[] exportVehicleModel(SearchVehicleModelApplyDTO searchVehicleModelApplyDTO);

    /**
     * 资料确认回滚
     * @param modelId 车型信息ID
     * @param orgId 操作人所属机构
     * @param userId 操作人ID
     * @param userName 操作人名称
     * @return 操作结果
     */
    BaseResponse datumRollback(String modelId, String orgId, String userId, String userName);

    /**
     * 车企上传车型登记申请表
     * @param uploadModelFileDTO
     */
    void uploadModelRegisterApplication(UploadModelFileDTO uploadModelFileDTO);


    /**
     * 车企上传平台符合性报告
     * @param uploadModelFileDTO
     */
    void uploadPlatformComplianceReport(UploadModelFileDTO uploadModelFileDTO);

    /**
     * 检测中心上传车型评估报告
     * @param uploadModelFileDTO
     */
    void uploadModelEvaluationReport(UploadModelFileDTO uploadModelFileDTO);

    /**
     * 车型受理
     * @param id 车型ID
     * @param orgId 操作人机构
     * @param userId 操作人ID
     * @param userName 操作人名称
     * @return
     */
    void accept(String id, String orgId, String userId, String userName);

    /**
     * 上传数据中心评估报告
     * @param uploadModelFileDTO 上传内容
     */
    void uploadDataCenterEvaluationReport(UploadModelFileDTO uploadModelFileDTO);

    /**
     * 上传检测中心评估报告
     * @param uploadModelFileDTO 上传内容
     */
    void uploadTestingCenterEvaluationReport(UploadModelFileDTO uploadModelFileDTO);

    /**
     * 提交数据中心评估报告
     * @param id
     * @param orgId
     * @param userId
     * @param userName
     */
    void submitDataCenterEvaluation(String id, String orgId, String userId, String userName);

    /**
     * 提交检测中心评估报告
     * @param id
     * @param orgId
     * @param userId
     * @param userName
     */
    void submitTestingCenterEvaluation(String id, String orgId, String userId, String userName);

    /**
     * 退回数据中心评估报告
     * @param id
     * @param orgId
     * @param userId
     * @param userName
     */
    void dataCenterEvaluationDeny(String id, String orgId, String userId, String userName);

    /**
     * 退回检测中心评估报告
     * @param id
     * @param orgId
     * @param userId
     * @param userName
     */
    void testingCenterEvaluationDeny(String id, String orgId, String userId, String userName);

    /**
     * 评估报告确认
     * @param id
     * @param orgId
     * @param userId
     * @param userName
     */
    void evaluationApprove(String id, String orgId, String userId, String userName);

    /**
     * 下载车型文件报告
     * @param ids
     * @return
     */
    FileResponse downloadVehicleModelFiles(List<String> ids);

    /**
     * 车型受理退回
     * @param id
     * @param orgId
     * @param userId
     * @param userName
     * @return
     */
    void accessReturn(String id, String orgId, String userId, String userName);

    /**
     * 终止车型
     * @param id
     * @param remark
     * @param userName
     */
    void terminate(String id, String remark,String userId,  String userName);
}
