package com.extracme.nevmp.service.vehicle.impl;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.api.ResultCode;
import com.extracme.nevmp.dto.RenewVehicleSellerDTO;
import com.extracme.nevmp.dto.common.*;
import com.extracme.nevmp.dto.vehicle.seller.*;
import com.extracme.nevmp.enums.DealerLogEnum;
import com.extracme.nevmp.enums.FileTypeEnum;
import com.extracme.nevmp.enums.VehicleSellerStatusEnum;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.mapper.VehicleDealerLogMapper;
import com.extracme.nevmp.mapper.extend.VehicleDealerExtendMapper;
import com.extracme.nevmp.model.VehicleDealer;
import com.extracme.nevmp.model.VehicleDealerLog;
import com.extracme.nevmp.service.FileService;
import com.extracme.nevmp.service.vehicle.VehicleDealerService;
import com.extracme.nevmp.utils.AssertUtil;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.extracme.nevmp.mapper.OrgInfoDynamicSqlSupport.orgInfo;
import static com.extracme.nevmp.mapper.VehicleDealerCooperateDynamicSqlSupport.vehicleDealerCooperate;
import static com.extracme.nevmp.mapper.VehicleDealerDynamicSqlSupport.vehicleDealer;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @Description
 */
@Service
public class VehicleDealerServiceImpl implements VehicleDealerService {

    @Autowired
    private VehicleDealerExtendMapper vehicleDealerMapper;
    @Autowired
    private VehicleDealerLogMapper logMapper;

    @Autowired
    FileService fileService;

    @Override
    public ComboInfoBO<String, String> comboVehicleDealer(String orgId) {
        SelectStatementProvider selectSql = select(vehicleDealer.dealerId, vehicleDealer.dealerName).from(vehicleDealer)
                .where(vehicleDealer.orgId, isEqualToWhenPresent(orgId))
                .and(vehicleDealer.status, isIn(
                        VehicleSellerStatusEnum.APPROVE.getValue(),
                        VehicleSellerStatusEnum.RENEW.getValue(),
                        VehicleSellerStatusEnum.RENEW_DENY.getValue()))
                .and(vehicleDealer.disable, isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<VehicleDealer> vehicleDealerList = vehicleDealerMapper.selectMany(selectSql);
        ComboInfoBO<String, String> comboInfoBO = new ComboInfoBO<>();
        List<ComboInfo<String, String>> list = new ArrayList<>();

        for (VehicleDealer dealer : vehicleDealerList) {
            ComboInfo<String, String> comboInfo = new ComboInfo<>(dealer.getDealerId(), dealer.getDealerName());
            list.add(comboInfo);
        }
        comboInfoBO.setComboBoxList(list);
        return comboInfoBO;
    }

    @Override
    public VehicleDealer queryVehicleDealer(String dealerName, String orgId) {
        SelectStatementProvider selectStatement = SqlBuilder.select(vehicleDealer.allColumns())
                .from(vehicleDealer)
                .where(vehicleDealer.dealerName, isEqualTo(dealerName))
                .and(vehicleDealer.orgId, isEqualTo(orgId))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<VehicleDealer> optionalVehicleDealer = vehicleDealerMapper.selectOne(selectStatement);
        return optionalVehicleDealer.orElse(null);
    }

    @Override
    public Map<String, VehicleDealer> queryVehicleDealerByDealerName(List<String> dealerNameList, String orgId) {
        if (dealerNameList == null || dealerNameList.isEmpty()) {
            return new HashMap<>();
        }
        SelectStatementProvider selectStatement = SqlBuilder.select(vehicleDealer.allColumns())
                .from(vehicleDealer)
                .where(vehicleDealer.dealerName, isIn(dealerNameList))
                .and(vehicleDealer.orgId, isEqualTo(orgId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final List<VehicleDealer> vehicleDealerList = vehicleDealerMapper.selectMany(selectStatement);
        return vehicleDealerList.stream().collect(Collectors.toMap(VehicleDealer::getDealerName, value -> value, (v1, v2) -> v1));
    }

    @Override
    public Boolean existAuthorizedForVehicleModel(String vehicleModelId, List<String> vehicleDealerId) {
        if (StringUtils.isBlank(vehicleModelId) || vehicleDealerId == null || vehicleDealerId.isEmpty()) {
            return false;
        }

        SelectStatementProvider countStatement = SqlBuilder.countFrom(vehicleDealerCooperate)
                .where(vehicleDealerCooperate.modelId, isEqualTo(vehicleModelId))
                .and(vehicleDealerCooperate.dealerId, isIn(vehicleDealerId))
                .and(vehicleDealerCooperate.status, isIn(VehicleSellerStatusEnum.APPROVE.getValue(), VehicleSellerStatusEnum.RENEW.getValue(), VehicleSellerStatusEnum.RENEW_DENY.getValue()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final long count = vehicleDealerMapper.count(countStatement);
        return count > 0;
    }


    @Override
    public BaseResponse saveVehicleSeller(SaveVehicleSellerDTO saveVehicleSellerDTO) {
        if (existSameDealerName(saveVehicleSellerDTO.getDealerName(), saveVehicleSellerDTO.getOrgId())) {
            throw new ServiceException("存在有效的相同名称的经销商");
        }
        VehicleDealer vehicleDealer = new VehicleDealer();
        BeanUtils.copyProperties(saveVehicleSellerDTO, vehicleDealer);
        vehicleDealer.setDealerId(UUID.randomUUID().toString());
        vehicleDealer.setCreatedUser(saveVehicleSellerDTO.getUserId());
        vehicleDealer.setCreatedUsername(saveVehicleSellerDTO.getUserName());
        vehicleDealer.setCreatedTime(new Date());
        vehicleDealer.setStatus(VehicleSellerStatusEnum.SAVE.getValue());
        vehicleDealer.setExpireTime(saveVehicleSellerDTO.getExpireTime());
        fileService.clearFile(FileTypeEnum.VEHICLE_SELLER_CONFIRM_COPY, vehicleDealer.getDealerId(), saveVehicleSellerDTO.getUserId(), saveVehicleSellerDTO.getUserName());
        fileService.saveFileInfo(FileTypeEnum.VEHICLE_SELLER_CONFIRM_COPY, saveVehicleSellerDTO.getDealerAuthorizeCopy(), vehicleDealer.getDealerId()
                , saveVehicleSellerDTO.getUserId(), saveVehicleSellerDTO.getUserName());
        vehicleDealerMapper.insertSelective(vehicleDealer);
        insertLog(vehicleDealer.getDealerId(), DealerLogEnum.ADD_DEALER, "");
        return new BaseResponse();
    }

    @Override
    public BaseResponse getVehicleSeller(String id, String orgId) {
        SelectStatementProvider selectSql = select(
                vehicleDealer.dealerId,
                vehicleDealer.dealerName,
                vehicleDealer.status,
                vehicleDealer.remark,
                vehicleDealer.dealerMail,
                vehicleDealer.dealerContacter,
                vehicleDealer.mobilePhone,
                vehicleDealer.expireTime,
                vehicleDealer.dealerAddress,
                vehicleDealer.dealerAuthorizeCopy)
                .from(vehicleDealer)
                .where(vehicleDealer.dealerId, isEqualTo(id))
                .and(vehicleDealer.orgId, isEqualTo(orgId))
                .and(vehicleDealer.flag, isEqualTo(1))
                .build().render(RenderingStrategies.MYBATIS3);
        Optional<VehicleDealer> vehicleSellerOptional = vehicleDealerMapper.selectOne(selectSql);
        if (!vehicleSellerOptional.isPresent()) {
            throw new ServiceException("不存在该经销商");
        }
        VehicleDealer vehicleDealer = vehicleSellerOptional.get();
        VehicleSellerDetailDTO vehicleSellerDetailDTO = new VehicleSellerDetailDTO();
        BeanUtils.copyProperties(vehicleDealer, vehicleSellerDetailDTO);
        return vehicleSellerDetailDTO;
    }


    @Override
    public BaseResponse updateVehicleSeller(UpdateVehicleSellerDTO updateVehicleSellerDTO) {
        Optional<VehicleDealer> vehicleDealer = vehicleDealerMapper.selectByPrimaryKey(updateVehicleSellerDTO.getDealerId());
        if (!vehicleDealer.isPresent() || !updateVehicleSellerDTO.getOrgId().equals(vehicleDealer.get().getOrgId())) {
            throw new ServiceException("不存在该记录");
        }
        if (!(vehicleDealer.get().getStatus().equals(0)
                || vehicleDealer.get().getStatus().equals(3))) {
            throw new ServiceException("只有录入中、拒绝才能修改");
        }
        if (existSameDealerName(updateVehicleSellerDTO.getDealerId(), updateVehicleSellerDTO.getDealerName(),updateVehicleSellerDTO.getOrgId())) {
            throw new ServiceException("存在相同名称的经销商");
        }
        VehicleDealer updateVehicleDealer = new VehicleDealer();
        BeanUtils.copyProperties(updateVehicleSellerDTO, updateVehicleDealer);
        updateVehicleDealer.setUpdatedUser(updateVehicleSellerDTO.getUserId());
        updateVehicleDealer.setUpdatedUsername(updateVehicleSellerDTO.getUserName());
        updateVehicleDealer.setUpdatedTime(new Date());
        fileService.clearFile(FileTypeEnum.VEHICLE_SELLER_CONFIRM_COPY, updateVehicleSellerDTO.getDealerId(), updateVehicleSellerDTO.getUserId(), updateVehicleSellerDTO.getUserName());
        fileService.saveFileInfo(FileTypeEnum.VEHICLE_SELLER_CONFIRM_COPY, updateVehicleSellerDTO.getDealerAuthorizeCopy(), updateVehicleSellerDTO.getDealerId()
                , updateVehicleSellerDTO.getUserId(), updateVehicleSellerDTO.getUserName());
        vehicleDealerMapper.updateByPrimaryKeySelective(updateVehicleDealer);
        insertLog(updateVehicleSellerDTO.getDealerId(), DealerLogEnum.UPDATE_DEALER, "");
        return new BaseResponse();
    }


    @Override
    public BaseResponse deleteVehicleSeller(DeleteVehicleSellerDTO deleteVehicleSellerDTO) {
        // 逻辑删除
        Optional<VehicleDealer> vehicleDealerOptional = vehicleDealerMapper.selectByPrimaryKey(deleteVehicleSellerDTO.getDealerId());
        if (!vehicleDealerOptional.isPresent()
                || vehicleDealerOptional.get().getFlag().equals(0)
                || !deleteVehicleSellerDTO.getOrgId().equals(vehicleDealerOptional.get().getOrgId())) {
            throw new ServiceException("不存在该记录");
        }
        UpdateStatementProvider updateSql = update(vehicleDealer)
                .set(vehicleDealer.flag).equalTo(0)
                .set(vehicleDealer.updatedUser).equalTo(deleteVehicleSellerDTO.getUserId())
                .set(vehicleDealer.updatedUsername).equalTo(deleteVehicleSellerDTO.getUserName())
                .where(vehicleDealer.dealerId, isEqualTo(deleteVehicleSellerDTO.getDealerId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleDealerMapper.update(updateSql);
        insertLog(deleteVehicleSellerDTO.getDealerId(), DealerLogEnum.DELETE_DEALER, "");
        return new BaseResponse();
    }


    @Override
    public PageInfoBO<VehicleDealerDTO> searchVehicleSeller(SearchVehicleSellerDTO searchVehicleSellerDTO) {
        SelectStatementProvider selectSql = select(
                vehicleDealer.dealerId,
                vehicleDealer.dealerName,
                vehicleDealer.status,
                vehicleDealer.remark,
                vehicleDealer.dealerContacter,
                vehicleDealer.mobilePhone,
                vehicleDealer.dealerAddress,
                vehicleDealer.expireTime,
                vehicleDealer.dealerAuthorizeCopy,
                vehicleDealer.renewTime,
                vehicleDealer.disable,
                vehicleDealer.renewAuthorizeCopy)
                .from(vehicleDealer)
                .where(vehicleDealer.dealerName, isEqualToWhenPresent(searchVehicleSellerDTO.getDealerName()))
                .and(vehicleDealer.orgId, isEqualTo(searchVehicleSellerDTO.getOrgId()))
                .and(vehicleDealer.status, isEqualToWhenPresent(searchVehicleSellerDTO.getStatus()))
                .and(vehicleDealer.disable, isEqualToWhenPresent(searchVehicleSellerDTO.getDisable()))
                .and(vehicleDealer.flag, isEqualTo(1))
                .limit(searchVehicleSellerDTO.getLimit()).offset(searchVehicleSellerDTO.getOffset())
                .build().render(RenderingStrategies.MYBATIS3);

        SelectStatementProvider countSql = select(count())
                .from(vehicleDealer)
                .where(vehicleDealer.dealerName, isEqualToWhenPresent(searchVehicleSellerDTO.getDealerName()))
                .and(vehicleDealer.status, isEqualToWhenPresent(searchVehicleSellerDTO.getStatus()))
                .and(vehicleDealer.disable, isEqualToWhenPresent(searchVehicleSellerDTO.getDisable()))
                .and(vehicleDealer.orgId, isEqualTo(searchVehicleSellerDTO.getOrgId()))
                .and(vehicleDealer.flag, isEqualTo(1))
                .build().render(RenderingStrategies.MYBATIS3);
        List<VehicleDealer> vehicleDealers = vehicleDealerMapper.selectMany(selectSql);
        Long count = vehicleDealerMapper.count(countSql);

        List<VehicleDealerDTO> list = new ArrayList<>();
        for (VehicleDealer dealer : vehicleDealers) {
            VehicleDealerDTO vehicleDealerDTO = new VehicleDealerDTO();
            vehicleDealerDTO.accept(dealer);
            list.add(vehicleDealerDTO);
        }
        PageInfoBO<VehicleDealerDTO> pageInfoBO = new PageInfoBO();
        pageInfoBO.setRows(list);
        pageInfoBO.setTotal(count);
        return pageInfoBO;
    }


    @Override
    public BaseResponse submitVehicleSeller(MultiVehicleSellerIdDTO multiVehicleSellerIdDTO) {
        if (multiVehicleSellerIdDTO.getIds() == null || multiVehicleSellerIdDTO.getIds().size() == 0) {
            throw new ServiceException("ID不能为空");
        }
        // 录入中和拒绝状态可以提交
        changeSellerStatus(multiVehicleSellerIdDTO.getIds(),
                Arrays.asList(VehicleSellerStatusEnum.SAVE, VehicleSellerStatusEnum.DENY),
                VehicleSellerStatusEnum.SUBMIT,
                multiVehicleSellerIdDTO.getUserId(),
                multiVehicleSellerIdDTO.getUserName());
        for (String id : multiVehicleSellerIdDTO.getIds()) {
            insertLog(id, DealerLogEnum.SUBMIT_DEALER, "");
        }
        return new BaseResponse();
    }


    @Override
    public BaseResponse approveVehicleSeller(MultiVehicleSellerIdDTO multiVehicleSellerIdDTO) {
        if (multiVehicleSellerIdDTO.getIds() == null || multiVehicleSellerIdDTO.getIds().size() == 0) {
            throw new ServiceException("ID不能为空");
        }
        SelectStatementProvider selectSql = countFrom(vehicleDealer)
                .where(vehicleDealer.status, isNotIn(VehicleSellerStatusEnum.SUBMIT.getValue(), VehicleSellerStatusEnum.RENEW.getValue()))
                .and(vehicleDealer.dealerId, isIn(multiVehicleSellerIdDTO.getIds()))
                .build().render(RenderingStrategies.MYBATIS3);
        long count = vehicleDealerMapper.count(selectSql);

        List<VehicleDealer> dealerList = vehicleDealerMapper.select(dealer -> dealer
                .where(vehicleDealer.dealerId, isIn(multiVehicleSellerIdDTO.getIds()))
                .and(vehicleDealer.status, isIn(VehicleSellerStatusEnum.SUBMIT.getValue(), VehicleSellerStatusEnum.RENEW.getValue())));
        for (VehicleDealer dealer : dealerList) {
            if (VehicleSellerStatusEnum.RENEW.getValue().equals(dealer.getStatus())) {
                // 续期通过
                dealer.setDealerAuthorizeCopy(dealer.getRenewAuthorizeCopy());
                dealer.setExpireTime(dealer.getRenewTime());
                fileService.clearFile(FileTypeEnum.VEHICLE_SELLER_CONFIRM_COPY, dealer.getDealerId(), multiVehicleSellerIdDTO.getUserId(), multiVehicleSellerIdDTO.getUserName());
                FileInfoDTO singleFileInfo = fileService.getSingleFileInfo(FileTypeEnum.VEHICLE_SELLER_RENEW_CONFIRM_COPY, dealer.getDealerId());
                fileService.saveFileInfo(FileTypeEnum.VEHICLE_SELLER_CONFIRM_COPY, singleFileInfo.getRelativeFilePath(), dealer.getDealerId(), multiVehicleSellerIdDTO.getUserId(), multiVehicleSellerIdDTO.getUserName());
            }
            dealer.setRemark(StringUtils.EMPTY);
            dealer.setStatus(VehicleSellerStatusEnum.APPROVE.getValue());
            vehicleDealerMapper.updateByPrimaryKeySelective(dealer);
            insertLog(dealer.getDealerId(), DealerLogEnum.APPROVE_DEALER, "");
        }
        if (count != 0L) {
            return BaseResponse.builder().message("存在" + count + "条记录状态不符合").resultCode(ResultCode.SUCCESS).build();
        }
        return new BaseResponse();
    }


    @Override
    public BaseResponse denyVehicleSeller(MultiVehicleSellerIdDTO multiVehicleSellerIdDTO, String reason) {
        if (multiVehicleSellerIdDTO.getIds() == null || multiVehicleSellerIdDTO.getIds().size() == 0) {
            throw new ServiceException("ID不能为空");
        }
        SelectStatementProvider selectSql = countFrom(vehicleDealer)
                .where(vehicleDealer.status, isNotIn(VehicleSellerStatusEnum.SUBMIT.getValue(), VehicleSellerStatusEnum.RENEW.getValue()))
                .and(vehicleDealer.dealerId, isIn(multiVehicleSellerIdDTO.getIds()))
                .build().render(RenderingStrategies.MYBATIS3);
        long count = vehicleDealerMapper.count(selectSql);

        List<VehicleDealer> dealerList = vehicleDealerMapper.select(dealer -> dealer.where(vehicleDealer.dealerId, isIn(multiVehicleSellerIdDTO.getIds())));
        for (VehicleDealer dealer : dealerList) {
            if (VehicleSellerStatusEnum.SUBMIT.getValue().equals(dealer.getStatus())) {
                // 拒绝
                dealer.setStatus(VehicleSellerStatusEnum.DENY.getValue());
                dealer.setRemark(reason);
            }else if (VehicleSellerStatusEnum.RENEW.getValue().equals(dealer.getStatus())) {
                // 续期拒绝
                dealer.setStatus(VehicleSellerStatusEnum.RENEW_DENY.getValue());
                dealer.setRemark(reason);
            }
            vehicleDealerMapper.updateByPrimaryKeySelective(dealer);
            insertLog(dealer.getDealerId(), DealerLogEnum.DENY_DEALER, "");
        }
        if (count != 0) {
            return BaseResponse.builder().message("存在" + count + "条记录状态不符合").resultCode(ResultCode.SUCCESS).build();
        }
        return new BaseResponse();
    }


    @Override
    public BaseResponse invalidVehicleSeller(MultiVehicleSellerIdDTO multiVehicleSellerIdDTO) {
        if (multiVehicleSellerIdDTO.getIds() == null || multiVehicleSellerIdDTO.getIds().size() != 1) {
            throw new ServiceException("ID只能为1个");
        }

        SelectStatementProvider selectSql = select(vehicleDealer.allColumns()).from(vehicleDealer)
                .where(vehicleDealer.flag, isEqualTo(1))
                .and(vehicleDealer.dealerId, isIn(multiVehicleSellerIdDTO.getIds()))
                .build().render(RenderingStrategies.MYBATIS3);
        Optional<VehicleDealer> vehicleSeller = vehicleDealerMapper.selectOne(selectSql);
        if (!(vehicleSeller.isPresent() && vehicleSeller.get().getStatus().equals(2))) {
            return BaseResponse.builder().message("经销商状态不符合").resultCode(ResultCode.SUCCESS).build();
        }
        UpdateStatementProvider updateSql = update(vehicleDealer)
                .set(vehicleDealer.disable).equalTo(2)
                .where(vehicleDealer.status, isEqualTo(VehicleSellerStatusEnum.APPROVE.getValue()))
                .and(vehicleDealer.flag, isEqualTo(1))
                .and(vehicleDealer.dealerId, isIn(multiVehicleSellerIdDTO.getIds()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleDealerMapper.update(updateSql);
        for (String id : multiVehicleSellerIdDTO.getIds()) {
            insertLog(id, DealerLogEnum.INVALID_DEALER, "");
        }
        return new BaseResponse();
    }


    @Override
    public BaseResponse searchApproveVehicleSeller(SearchApproveVehicleSellerDTO searchApproveVehicleSellerDTO) {
        //模糊查询经销商地址
        if(StringUtils.isNotBlank(searchApproveVehicleSellerDTO.getSellerAddress())){
            searchApproveVehicleSellerDTO.setSellerAddress("%" + searchApproveVehicleSellerDTO.getSellerAddress() + "%");
        }
        SelectStatementProvider selectSql = select(
                vehicleDealer.dealerId,
                vehicleDealer.dealerName,
                vehicleDealer.dealerContacter,
                vehicleDealer.dealerAddress,
                vehicleDealer.dealerAuthorizeCopy,
                vehicleDealer.mobilePhone,
                vehicleDealer.status,
                orgInfo.orgName,
                vehicleDealer.remark,
                vehicleDealer.disable,
                vehicleDealer.expireTime,
                vehicleDealer.renewTime,
                vehicleDealer.renewAuthorizeCopy
        ).from(vehicleDealer)
                .leftJoin(orgInfo).on(vehicleDealer.orgId, equalTo(orgInfo.orgId))
                .where(vehicleDealer.dealerName, isEqualToWhenPresent(searchApproveVehicleSellerDTO.getDealerName()))
                .and(vehicleDealer.status, isEqualToWhenPresent(searchApproveVehicleSellerDTO.getStatus()))
                .and(vehicleDealer.disable, isEqualToWhenPresent(searchApproveVehicleSellerDTO.getDisable()))
                .and(vehicleDealer.orgId, isEqualToWhenPresent(searchApproveVehicleSellerDTO.getOrgId()))
                .and(vehicleDealer.flag, isEqualTo(1))
                .and(vehicleDealer.status, isNotEqualTo(0))
                .and(vehicleDealer.dealerAddress, isLikeWhenPresent(searchApproveVehicleSellerDTO.getSellerAddress()))
                .limit(searchApproveVehicleSellerDTO.getLimit()).offset(searchApproveVehicleSellerDTO.getOffset())
                .build().render(RenderingStrategies.MYBATIS3);
        List<ApproveVehicleDealerDTO> approveVehicleDealerDTOS = vehicleDealerMapper.searchDealer(selectSql);

        SelectStatementProvider countSql = select(count()).from(vehicleDealer)
                .where(vehicleDealer.dealerName, isEqualToWhenPresent(searchApproveVehicleSellerDTO.getDealerName()))
                .and(vehicleDealer.status, isEqualToWhenPresent(searchApproveVehicleSellerDTO.getStatus()))
                .and(vehicleDealer.disable, isEqualToWhenPresent(searchApproveVehicleSellerDTO.getDisable()))
                .and(vehicleDealer.orgId, isEqualToWhenPresent(searchApproveVehicleSellerDTO.getOrgId()))
                .and(vehicleDealer.flag, isEqualTo(1))
                .and(vehicleDealer.status, isNotEqualTo(0))
                .and(vehicleDealer.dealerAddress, isLikeWhenPresent(searchApproveVehicleSellerDTO.getSellerAddress()))
                .build().render(RenderingStrategies.MYBATIS3);
        Long count = vehicleDealerMapper.count(countSql);
        PageInfoBO pageInfoBO = new PageInfoBO();
        pageInfoBO.setTotal(count);
        pageInfoBO.setRows(approveVehicleDealerDTOS);
        return pageInfoBO;
    }


    @Override
    public FileInfoBO querySellerConfirmCopy(QuerySellerConfirmDTO querySellerConfirmDTO) {
        Optional<VehicleDealer> vehicleSeller = vehicleDealerMapper.selectByPrimaryKey(querySellerConfirmDTO.getDealerId());
        //查询上传的扫描件
        final List<FileInfoDTO> fileInfoDTOList = fileService.getFileInfo(FileTypeEnum.VEHICLE_SELLER_CONFIRM_COPY, querySellerConfirmDTO.getDealerId());
        return new FileInfoBO(fileInfoDTOList);
    }

    @Override
    public BaseResponse renewVehicleSeller(RenewVehicleSellerDTO renewVehicleSellerDTO) {
        Optional<VehicleDealer> vehicleDealerOptional = vehicleDealerMapper.selectByPrimaryKey(renewVehicleSellerDTO.getDealerId());
        AssertUtil.isTrue(vehicleDealerOptional.isPresent(), "该经销商不存在");
        AssertUtil.isEquals(vehicleDealerOptional.get().getFlag(), 1, "该经销商不存在");
        AssertUtil.isEquals(vehicleDealerOptional.get().getDisable(), 1, "该经销商已失效");
        AssertUtil.isIn(vehicleDealerOptional.get().getStatus(),
                Arrays.asList(VehicleSellerStatusEnum.APPROVE.getValue(), VehicleSellerStatusEnum.RENEW_DENY.getValue()),
                "状态为通过和续期审核拒绝才能申请续期");
        fileService.clearFile(FileTypeEnum.VEHICLE_SELLER_RENEW_CONFIRM_COPY, renewVehicleSellerDTO.getDealerId(), renewVehicleSellerDTO.getUserId(), renewVehicleSellerDTO.getUserName());
        fileService.saveFileInfo(FileTypeEnum.VEHICLE_SELLER_RENEW_CONFIRM_COPY,renewVehicleSellerDTO.getRenewAuthorityCopy(), renewVehicleSellerDTO.getDealerId(), renewVehicleSellerDTO.getUserId(), renewVehicleSellerDTO.getUserName());
        VehicleDealer vehicleDealer = vehicleDealerOptional.get();
        vehicleDealer.setRenewTime(renewVehicleSellerDTO.getRenewTime());
        vehicleDealer.setRenewAuthorizeCopy("MOVE_UPLOAD_FILE_INFO");
        vehicleDealer.setStatus(VehicleSellerStatusEnum.RENEW.getValue());
        vehicleDealer.setMobilePhone(renewVehicleSellerDTO.getMobilePhone());
        vehicleDealer.setDealerAddress(renewVehicleSellerDTO.getDealerAddress());
        vehicleDealer.setDealerMail(renewVehicleSellerDTO.getDealerMail());
        vehicleDealer.setDealerContacter(renewVehicleSellerDTO.getDealerContacter());
        vehicleDealer.setUpdatedTime(new Date());
        vehicleDealer.setUpdatedUser(renewVehicleSellerDTO.getUserId());
        vehicleDealer.setUpdatedUsername(renewVehicleSellerDTO.getUserName());
        vehicleDealerMapper.updateByPrimaryKeySelective(vehicleDealer);
        insertLog(vehicleDealer.getDealerId(), DealerLogEnum.RENEW_DEALER, "");
        return new BaseResponse();
    }

    @Override
    public List<VehicleDealer> queryVehicleDealerList(String vehSeller, String orgId) {
        if(StringUtils.isEmpty(vehSeller) || StringUtils.isEmpty(orgId)){
            return new ArrayList<>();
        }
        SelectStatementProvider selectStatement = SqlBuilder.select(vehicleDealer.allColumns())
                .from(vehicleDealer)
                .where(vehicleDealer.dealerName, isEqualTo(vehSeller))
                .and(vehicleDealer.orgId, isEqualTo(orgId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleDealerMapper.selectMany(selectStatement);
    }

    /**
     * 更改经销商状态
     *
     * @param dearlerIds
     * @param expect
     * @param update
     * @param userId
     * @param userName
     * @return
     */
    private Integer changeSellerStatus(List<String> dearlerIds, VehicleSellerStatusEnum expect, VehicleSellerStatusEnum update, String userId, String userName) {
        return changeSellerStatus(dearlerIds, Collections.singletonList(expect), update, userId, userName);
    }

    private Integer changeSellerStatus(List<String> dearlerIds, List<VehicleSellerStatusEnum> expect, VehicleSellerStatusEnum update, String userId, String userName) {
        List<Integer> status = expect.stream().map(VehicleSellerStatusEnum::getValue).collect(Collectors.toList());
        UpdateStatementProvider updateSql = update(vehicleDealer)
                .set(vehicleDealer.status).equalTo(update.getValue())
                .set(vehicleDealer.updatedUser).equalTo(userId)
                .set(vehicleDealer.updatedUsername).equalTo(userName)
                .set(vehicleDealer.updatedTime).equalTo(new Date())
                .where(vehicleDealer.status, isIn(status))
                .and(vehicleDealer.flag, isEqualTo(1))
                .and(vehicleDealer.dealerId, isIn(dearlerIds))
                .build().render(RenderingStrategies.MYBATIS3);
        return vehicleDealerMapper.update(updateSql);
    }

    /**
     * 是否存在相同名称的经销商
     *
     * @param sellerName
     * @return
     */
    private Boolean existSameDealerName(String sellerName, String orgId) {
        SelectStatementProvider count = select(count()).from(vehicleDealer)
                .where(vehicleDealer.dealerName, isEqualTo(sellerName))
                .and(vehicleDealer.orgId, isEqualTo(orgId))
                .and(vehicleDealer.flag, isEqualTo(1))
                .and(vehicleDealer.disable, isEqualTo(1))
                .build().render(RenderingStrategies.MYBATIS3);
        return vehicleDealerMapper.count(count) > 0;
    }


    /**
     * 是否存在相同名称的经销商
     *
     * @param dealerName
     * @return
     */
    private Boolean existSameDealerName(String dealerId, String dealerName, String orgId) {
        SelectStatementProvider count = select(count()).from(vehicleDealer)
                .where(vehicleDealer.dealerName, isEqualTo(dealerName))
                .and(vehicleDealer.dealerId, isNotEqualTo(dealerId))
                .and(vehicleDealer.orgId, isEqualTo(orgId))
                .and(vehicleDealer.flag, isEqualTo(1))
                .and(vehicleDealer.disable, isEqualTo(1))
                .build().render(RenderingStrategies.MYBATIS3);
        return vehicleDealerMapper.count(count) > 0;
    }


    private void insertLog(String dealerId, DealerLogEnum dealerLogEnum, String content) {
        VehicleDealerLog dealerLog = new VehicleDealerLog();
        dealerLog.setType(1);
        dealerLog.setDealerId(dealerId);
        dealerLog.setOperateType(dealerLogEnum.getType());
        Supplier<String> supplier = () -> {
            if (StringUtils.isNotBlank(content))
                return " : " + content;
            return "";
        };
        dealerLog.setOperateContent(dealerLogEnum.getValue() + supplier.get());
        dealerLog.setCreateTime(new Date());
    }

}
