package com.extracme.nevmp.service.vehicle;

import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.dto.RenewVehicleSellerDTO;
import com.extracme.nevmp.dto.common.ComboInfoBO;
import com.extracme.nevmp.dto.common.FileInfoBO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.vehicle.seller.*;
import com.extracme.nevmp.model.VehicleDealer;
import com.extracme.nevmp.model.VehicleSeller;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 */
public interface VehicleDealerService {

    /**
     * 经销商下拉框
     * @param orgId
     * @return
     */
    ComboInfoBO<String, String> comboVehicleDealer(String orgId);

    /**
     * 根据经销商名称查询经销商信息
     * @param sellerName
     * @param orgId
     * @return
     */
    VehicleDealer queryVehicleDealer(String sellerName, String orgId);

    /**
     * 查询经销商Map
     * @param dealerNameList
     * @param orgId
     * @return
     */
    Map<String, VehicleDealer> queryVehicleDealerByDealerName(List<String> dealerNameList, String orgId);


    /**
     * 存在对该车型授权的经销商信息
     * @param vehicleModelId 车型ID
     * @param vehicleDealerId 经销商ID列表，有任意满足即可
     * @return true:存在授权 false:不存在
     */
    Boolean existAuthorizedForVehicleModel(String vehicleModelId, List<String> vehicleDealerId);

    /**
     * 保存经销商信息
     * @param saveVehicleSellerDTO
     * @return
     */
    BaseResponse saveVehicleSeller(SaveVehicleSellerDTO saveVehicleSellerDTO);

    /**
     * 查询经销商信息详情
     * @param id
     * @param orgId
     * @return
     */
    BaseResponse getVehicleSeller(String id, String orgId);

    /**
     * 更新经销
     * @param updateVehicleSellerDTO
     * @return
     */
    BaseResponse updateVehicleSeller(UpdateVehicleSellerDTO updateVehicleSellerDTO);

    /**
     * 删除经销商
     * @param deleteVehicleSellerDTO
     * @return
     */
    BaseResponse deleteVehicleSeller(DeleteVehicleSellerDTO deleteVehicleSellerDTO);

    /**
     * 查询经销商信息
     * @param searchVehicleSellerDTO
     * @return
     */
    PageInfoBO<VehicleDealerDTO> searchVehicleSeller(SearchVehicleSellerDTO searchVehicleSellerDTO);

    /**
     * 车企提交经销商信息
     * @param multiVehicleSellerIdDTO
     * @return
     */
    BaseResponse submitVehicleSeller(MultiVehicleSellerIdDTO multiVehicleSellerIdDTO);

    /**
     * 审核通过
     * @param multiVehicleSellerIdDTO
     * @return
     */
    BaseResponse approveVehicleSeller(MultiVehicleSellerIdDTO multiVehicleSellerIdDTO);

    /**
     * 审核拒绝
     * @param multiVehicleSellerIdDTO
     * @param reason
     * @return
     */
    BaseResponse denyVehicleSeller(MultiVehicleSellerIdDTO multiVehicleSellerIdDTO, String reason);

    /**
     * 失效
     * @param multiVehicleSellerIdDTO
     * @return
     */
    BaseResponse invalidVehicleSeller(MultiVehicleSellerIdDTO multiVehicleSellerIdDTO);

    /**
     * 查询提交后的列表
     * @param searchApproveVehicleSellerDTO
     * @return
     */
    BaseResponse searchApproveVehicleSeller(SearchApproveVehicleSellerDTO searchApproveVehicleSellerDTO);

    /**
     * 查询证明材料
     * @param querySellerConfirmDTO
     * @return
     */
    FileInfoBO querySellerConfirmCopy(QuerySellerConfirmDTO querySellerConfirmDTO);

    /**
     * 续期
     * @param renewVehicleSellerDTO
     * @return
     */
    BaseResponse renewVehicleSeller(RenewVehicleSellerDTO renewVehicleSellerDTO);

    /**
     * 根据经销商名称查询关联的经销商列表
     * @param vehSeller 经销商名称
     * @param orgId 经销商关联企业ID
     * @return 经销商列表
     */
    List<VehicleDealer> queryVehicleDealerList(String vehSeller, String orgId);
}
