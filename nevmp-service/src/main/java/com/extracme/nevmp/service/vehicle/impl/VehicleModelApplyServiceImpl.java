package com.extracme.nevmp.service.vehicle.impl;

import static com.extracme.nevmp.mapper.VehicleModel2017DynamicSqlSupport.vehicleModel2017;
import static com.extracme.nevmp.mapper.VehicleModelOperateLogDynamicSqlSupport.vehicleModelOperateLog;
import static org.mybatis.dynamic.sql.SqlBuilder.countFrom;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThanWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.update;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.EasyExcel;
import com.extracme.nevmp.anno.ModelLog;
import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.constant.Const;
import com.extracme.nevmp.dto.common.FileInfoBO;
import com.extracme.nevmp.dto.common.FileInfoDTO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.file.FileResponse;
import com.extracme.nevmp.dto.vehicle.model.CommitModelDTO;
import com.extracme.nevmp.dto.vehicle.model.DatumConfirmDTO;
import com.extracme.nevmp.dto.vehicle.model.ExportVehicleModelDTO;
import com.extracme.nevmp.dto.vehicle.model.GetVehicleModelFileDTO;
import com.extracme.nevmp.dto.vehicle.model.ModelRemarkDTO;
import com.extracme.nevmp.dto.vehicle.model.SearchVehicleModelApplyDTO;
import com.extracme.nevmp.dto.vehicle.model.SearchVehicleModelDTO;
import com.extracme.nevmp.dto.vehicle.model.UploadModelFileDTO;
import com.extracme.nevmp.dto.vehicle.model.VehicleModelBaseDTO;
import com.extracme.nevmp.dto.vehicle.model.VehicleModelFactory;
import com.extracme.nevmp.enums.FileTypeEnum;
import com.extracme.nevmp.enums.ModelLogTypeEnum;
import com.extracme.nevmp.enums.VehicleApplyStatusEnum;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.mapper.VehicleModel2017Mapper;
import com.extracme.nevmp.mapper.VehicleModelOperateLogMapper;
import com.extracme.nevmp.model.OrgInfo;
import com.extracme.nevmp.model.UploadFileInfo;
import com.extracme.nevmp.model.VehicleModel2017;
import com.extracme.nevmp.model.VehicleModelOperateLog;
import com.extracme.nevmp.service.FileService;
import com.extracme.nevmp.service.OrganizationService;
import com.extracme.nevmp.service.vehicle.VehicleModelApplyService;
import com.extracme.nevmp.utils.AssertUtil;
import com.extracme.nevmp.utils.DateUtil;
import com.extracme.nevmp.utils.ExcelUtil;
import com.extracme.nevmp.utils.FileUtil;
import com.extracme.nevmp.utils.OSSFileUtils;
import com.extracme.nevmp.utils.PDFUtilsForVehicleModel;
import com.extracme.nevmp.utils.TransUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 车型信息审批
 *
 * <AUTHOR>
 * @date 2020/11/10
 */
@Service
@Slf4j
public class VehicleModelApplyServiceImpl implements VehicleModelApplyService {

    @Autowired
    private VehicleModel2017Mapper vehicleModel2017Mapper;

    @Autowired
    private VehicleModelOperateLogMapper vehicleModelOperateLogMapper;

    @Autowired
    private FileService fileService;

    @Autowired
    private OrganizationService organizationService;

    @Override
    @Transactional
    @ModelLog(type = ModelLogTypeEnum.ADD_MODEL, model = "#model.vehicleModelId", org = "#model.orgId",
            uId = "#model.createdUser", uName = "#model.createdUserName")
    public BaseResponse addVehicleModel(VehicleModel2017 model) {
        Optional<VehicleModel2017> vehicleModel2017 = vehicleModel2017Mapper.selectByPrimaryKey(model.getVehicleModelId());
        AssertUtil.isTrue(!vehicleModel2017.isPresent(), "该车型编号已存在");
        OrgInfo orgInfo = organizationService.getOrgInfo(model.getOrgProductId());
        if(orgInfo != null){
            model.setOrgProductName(orgInfo.getOrgName());
        }
        model.setVehicleStatus(VehicleApplyStatusEnum.MODEL_ENTERING.getStatus());
        model.setSubsidyStatus(0);
        vehicleModel2017Mapper.insertSelective(model);
        return new BaseResponse();
    }


    @Override
    public BaseResponse getVehicleModelDetail(String id, String orgId) {
        Optional<VehicleModel2017> vehicleModel2017 = vehicleModel2017Mapper.selectByPrimaryKey(id);
        AssertUtil.isTrue(vehicleModel2017.isPresent(), "不存在该记录，请刷新后重试");
        AssertUtil.isTrue(Objects.nonNull(vehicleModel2017.get().getVehicleType()), "未知车辆类型");
        VehicleModelBaseDTO model = VehicleModelFactory.createModel(vehicleModel2017.get().getVehicleType());
        BeanUtils.copyProperties(vehicleModel2017.get(), model);
        return model;
    }

    @Override
    public BaseResponse searchVehicleModel(SearchVehicleModelApplyDTO searchVehicleModelApplyDTO) {
        SelectStatementProvider searchVehicleModel = select(vehicleModel2017.allColumns())
                .from(vehicleModel2017)
                .where(vehicleModel2017.vehicleModelId, isLikeWhenPresent(() -> {
                    if (StringUtils.isNotBlank(searchVehicleModelApplyDTO.getVehicleModelId())) {
                        return "%" + searchVehicleModelApplyDTO.getVehicleModelId() + "%";
                    }
                    return null;
                }))
                .and(vehicleModel2017.vehicleType, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleType()))
                .and(vehicleModel2017.vehicleStatus, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleStatus()))
                .and(vehicleModel2017.orgId, isEqualTo(searchVehicleModelApplyDTO.getOrgId()))
                .limit(searchVehicleModelApplyDTO.getLimit()).offset(searchVehicleModelApplyDTO.getOffset())
                .build().render(RenderingStrategies.MYBATIS3);
        List<VehicleModel2017> vehicleModel2017List = vehicleModel2017Mapper.selectMany(searchVehicleModel);
        List<SearchVehicleModelDTO> list = new ArrayList<>();
        //处理返回结果
        for (VehicleModel2017 model2017 : vehicleModel2017List) {
            SearchVehicleModelDTO searchVehicleModelDTO = new SearchVehicleModelDTO();
            BeanUtils.copyProperties(model2017, searchVehicleModelDTO);
            if (StringUtils.isNotBlank(searchVehicleModelDTO.getRemarkOrg())) {
                //机构备注？
                searchVehicleModelDTO.setHasOtherConfig(true);
            }
            list.add(searchVehicleModelDTO);
        }

        //查询记录数
        SelectStatementProvider countVehicleModel = countFrom(vehicleModel2017)
                .where(vehicleModel2017.vehicleModelId, isLikeWhenPresent(() -> {
                    if (StringUtils.isNotBlank(searchVehicleModelApplyDTO.getVehicleModelId())) {
                        return "%" + searchVehicleModelApplyDTO.getVehicleModelId() + "%";
                    }
                    return null;
                }))
                .and(vehicleModel2017.vehicleType, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleType()))
                .and(vehicleModel2017.vehicleStatus, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleStatus()))
                .and(vehicleModel2017.orgId, isEqualTo(searchVehicleModelApplyDTO.getOrgId()))
                .build().render(RenderingStrategies.MYBATIS3);
        long count = vehicleModel2017Mapper.count(countVehicleModel);

        //封装返回信息
        PageInfoBO pageInfoBO = new PageInfoBO<>();
        pageInfoBO.setRows(list);
        pageInfoBO.setTotal(count);
        return pageInfoBO;
    }


    @Override
    public BaseResponse approveVehicleModel(SearchVehicleModelApplyDTO searchVehicleModelApplyDTO) {
        //查询列表
        SelectStatementProvider searchVehicleModel = select(vehicleModel2017.allColumns()).from(vehicleModel2017)
                .where(vehicleModel2017.vehicleModelId, isLikeWhenPresent(() -> {
                    if (StringUtils.isNotBlank(searchVehicleModelApplyDTO.getVehicleModelId())) {
                        return "%" + searchVehicleModelApplyDTO.getVehicleModelId() + "%";
                    }
                    return null;
                }))
                .and(vehicleModel2017.vehicleType, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleType()))
                .and(vehicleModel2017.vehicleStatus, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleStatus()))
                .and(vehicleModel2017.orgProductId, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleOrgId()))
                .and(vehicleModel2017.orgId, isEqualToWhenPresent(searchVehicleModelApplyDTO.getOrgId()))
                .and(vehicleModel2017.vehicleStatus, isIn(searchVehicleModelApplyDTO.getBoundStatus()))
                .and(vehicleModel2017.vehicleFirstRegTime, isGreaterThanOrEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleFirstRegTimeStart()))
                .and(vehicleModel2017.vehicleFirstRegTime, isLessThanWhenPresent(searchVehicleModelApplyDTO.getVehicleFirstRegTimeEnd()))
                .and(vehicleModel2017.modelRegisterTime, isGreaterThanOrEqualToWhenPresent(searchVehicleModelApplyDTO.getModelRegisterTimeStart()))
                .and(vehicleModel2017.modelRegisterTime, isLessThanWhenPresent(searchVehicleModelApplyDTO.getModelRegisterTimeEnd()))
                .and(vehicleModel2017.modelEvaluationTime, isGreaterThanOrEqualToWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationTimeStart()))
                .and(vehicleModel2017.modelEvaluationTime, isLessThanWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationTimeEnd()))
                .and(vehicleModel2017.modelEvaluationStatus, isEqualToWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationStatus()))
                .and(vehicleModel2017.modelEvaluationDataSubmitTime, isGreaterThanOrEqualToWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationDataSubmitTimeStart()))
                .and(vehicleModel2017.modelEvaluationDataSubmitTime, isLessThanWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationDataSubmitTimeEnd()))
                .and(vehicleModel2017.modelEvaluationTestingSubmitTime, isGreaterThanOrEqualToWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationTestingSubmitTimeStart()))
                .and(vehicleModel2017.modelEvaluationTestingSubmitTime, isLessThanWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationTestingSubmitTimeEnd()))
                .orderBy(vehicleModel2017.orgProductName)
                .limit(searchVehicleModelApplyDTO.getLimit()).offset(searchVehicleModelApplyDTO.getOffset())
                .build().render(RenderingStrategies.MYBATIS3);
        List<VehicleModel2017> vehicleModel2017List = vehicleModel2017Mapper.selectMany(searchVehicleModel);
        List<SearchVehicleModelDTO> list = new ArrayList<>();
        for (VehicleModel2017 model2017 : vehicleModel2017List) {
            SearchVehicleModelDTO searchVehicleModelDTO = new SearchVehicleModelDTO();
            BeanUtils.copyProperties(model2017, searchVehicleModelDTO);
            if (StringUtils.isNotBlank(searchVehicleModelDTO.getRemarkOrg())) {
                searchVehicleModelDTO.setHasOtherConfig(true);
            }
            list.add(searchVehicleModelDTO);
        }


        //查询记录总数
        SelectStatementProvider countVehicleModel = countFrom(vehicleModel2017)
                .where(vehicleModel2017.vehicleModelId, isLikeWhenPresent(() -> {
                    if (StringUtils.isNotBlank(searchVehicleModelApplyDTO.getVehicleModelId())) {
                        return "%" + searchVehicleModelApplyDTO.getVehicleModelId() + "%";
                    }
                    return null;
                }))
                .and(vehicleModel2017.vehicleType, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleType()))
                .and(vehicleModel2017.vehicleStatus, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleStatus()))
                .and(vehicleModel2017.orgProductId, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleOrgId()))
                .and(vehicleModel2017.orgId, isEqualToWhenPresent(searchVehicleModelApplyDTO.getOrgId()))
                .and(vehicleModel2017.vehicleStatus, isIn(searchVehicleModelApplyDTO.getBoundStatus()))
                .and(vehicleModel2017.vehicleFirstRegTime, isGreaterThanOrEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleFirstRegTimeStart()))
                .and(vehicleModel2017.vehicleFirstRegTime, isLessThanWhenPresent(searchVehicleModelApplyDTO.getVehicleFirstRegTimeEnd()))
                .and(vehicleModel2017.modelRegisterTime, isGreaterThanOrEqualToWhenPresent(searchVehicleModelApplyDTO.getModelRegisterTimeStart()))
                .and(vehicleModel2017.modelRegisterTime, isLessThanWhenPresent(searchVehicleModelApplyDTO.getModelRegisterTimeEnd()))
                .and(vehicleModel2017.modelEvaluationTime, isGreaterThanOrEqualToWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationTimeStart()))
                .and(vehicleModel2017.modelEvaluationTime, isLessThanWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationTimeEnd()))
                .and(vehicleModel2017.modelEvaluationStatus, isEqualToWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationStatus()))
                .and(vehicleModel2017.modelEvaluationDataSubmitTime, isGreaterThanOrEqualToWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationDataSubmitTimeStart()))
                .and(vehicleModel2017.modelEvaluationDataSubmitTime, isLessThanWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationDataSubmitTimeEnd()))
                .and(vehicleModel2017.modelEvaluationTestingSubmitTime, isGreaterThanOrEqualToWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationTestingSubmitTimeStart()))
                .and(vehicleModel2017.modelEvaluationTestingSubmitTime, isLessThanWhenPresent(searchVehicleModelApplyDTO.getModelEvaluationTestingSubmitTimeEnd()))
                .build().render(RenderingStrategies.MYBATIS3);
        long count = vehicleModel2017Mapper.count(countVehicleModel);
        PageInfoBO pageInfoBO = new PageInfoBO<>();
        pageInfoBO.setRows(list);
        pageInfoBO.setTotal(count);
        return pageInfoBO;
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.UPDATE_MODEL, model = "#model.vehicleModelId", org = "#model.orgId",
            uId = "#model.createdUser", uName = "#model.createdUserName")
    public BaseResponse updateVehicleModel(VehicleModel2017 model) {
        AssertUtil.hasText(model.getVehicleModelId(), "车型编号不能为空");
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(model.getVehicleModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        AssertUtil.isTrue(vehicleModelOptional.get().getOrgId().equals(model.getOrgId()), "记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        if (!canUpdateModelByVehicleOrg(vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不允许修改车型信息");
        }
        vehicleModel2017Mapper.updateByPrimaryKeySelective(model);
        return new BaseResponse();
    }

    @Override
    public BaseResponse deleteVehicleModel(String modelId, String orgId) {
        AssertUtil.hasText(modelId, "车型编号为空");
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelId);
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "记录不存在，请刷新后重试");
        AssertUtil.isTrue(vehicleModelOptional.get().getOrgId().equals(orgId), "记录不存在，请刷新后重试");
        // 任何状态都可以删除吗
        AssertUtil.isTrue(canUpdateModelByVehicleOrg(vehicleModelOptional.get().getVehicleStatus()), "当前状态不可删除");
        vehicleModel2017Mapper.deleteByPrimaryKey(modelId);
        return new BaseResponse();
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.UPLOAD_APPLY_DOC, model = "#modelInfo.modelId", org = "#modelInfo.orgId",
            uId = "#modelInfo.userId", uName = "#modelInfo.userName")
    public BaseResponse uploadApplyDoc(UploadModelFileDTO modelInfo) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        AssertUtil.isTrue(vehicleModelOptional.get().getOrgId().equals(modelInfo.getOrgId()), "记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        if (!canUpdateModelByVehicleOrg(vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不允许上传申请书");
        }
        fileService.clearFile(FileTypeEnum.VEHICLE_MODEL_APPLY_DOC, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.VEHICLE_MODEL_APPLY_DOC, modelInfo.getPath(), modelInfo.getModelId(),
                modelInfo.getUserId(), modelInfo.getUserName());
        UpdateStatementProvider uploadApplyDoc = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.applicationCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(uploadApplyDoc);
        return new BaseResponse();
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.UPLOAD_CONFIRM_DOC, model = "#modelInfo.modelId", org = "#modelInfo.orgId",
            uId = "#modelInfo.userId", uName = "#modelInfo.userName")
    public BaseResponse uploadConfirmDoc(UploadModelFileDTO modelInfo) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        AssertUtil.isTrue(vehicleModelOptional.get().getOrgId().equals(modelInfo.getOrgId()), "记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        if (!canUpdateModelByVehicleOrg(vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不允许上传证明材料");
        }


        fileService.clearFile(FileTypeEnum.VEHICLE_MODEL_CONFIRM_DOC, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.VEHICLE_MODEL_CONFIRM_DOC, modelInfo.getPath(), modelInfo.getModelId(),
                modelInfo.getUserId(), modelInfo.getUserName());


        UpdateStatementProvider uploadApplyDoc = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.applicationDetailCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(uploadApplyDoc);


        return new BaseResponse();
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.REMARK, model = "#modelRemark.modelId", org = "#modelRemark.orgId",
            uId = "#modelRemark.userId", uName = "#modelRemark.userName")
    public BaseResponse remark(ModelRemarkDTO modelRemark) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelRemark.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        UpdateDSL<UpdateModel> updateModelUpdateDSL = null;
        if (modelRemark.getType().equals(1)) {
            updateModelUpdateDSL = update(vehicleModel2017).set(vehicleModel2017.remarkOrg).equalTo(modelRemark.getRemark());
        } else if (modelRemark.getType().equals(2)) {
            updateModelUpdateDSL = update(vehicleModel2017).set(vehicleModel2017.remarkDetect).equalTo(modelRemark.getRemark());
        } else if (modelRemark.getType().equals(3)) {
            updateModelUpdateDSL = update(vehicleModel2017).set(vehicleModel2017.remarkEitc).equalTo(modelRemark.getRemark());
        }
        if (updateModelUpdateDSL != null) {
            UpdateStatementProvider updateRemark = updateModelUpdateDSL.where(vehicleModel2017.vehicleModelId, isEqualTo(modelRemark.getModelId()))
                    .build().render(RenderingStrategies.MYBATIS3);
            vehicleModel2017Mapper.update(updateRemark);
        } else {
            throw new ServiceException("备注类型不可为空");
        }
        return new BaseResponse();
    }

    @Override
    @Transactional
    @ModelLog(type = ModelLogTypeEnum.COMMIT, model = "#modelInfo.modelId", org = "#modelInfo.orgId",
            uId = "#modelInfo.userId", uName = "#modelInfo.userName")
    public BaseResponse commitOne(CommitModelDTO modelInfo) {
        //校验
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), modelInfo.getModelId() + ": 该记录不存在，请刷新后重试");
        AssertUtil.isTrue(vehicleModelOptional.get().getOrgId().equals(modelInfo.getOrgId()), modelInfo.getModelId() + ": 该记录不存在，请刷新后重试");

        if(StringUtils.isBlank(vehicleModelOptional.get().getModelRegisterApplicationCopy())){
            throw new ServiceException(modelInfo.getModelId() + "：未上传车型登记申请表");
        }

        if(StringUtils.isBlank(vehicleModelOptional.get().getApplicationDetailCopy())){
            throw new ServiceException(modelInfo.getModelId() + "：未上传佐证材料");
        }


        UpdateStatementProvider updateVehicleStatus = null;
        if (VehicleApplyStatusEnum.MODEL_ENTERING.getStatus().equals(vehicleModelOptional.get().getVehicleStatus())
                || VehicleApplyStatusEnum.MODEL_ACCESS_DENY.getStatus().equals(vehicleModelOptional.get().getVehicleStatus())) {
            // 录入中和对接失败， 重新申请， 进入申请中
            updateVehicleStatus = update(vehicleModel2017)
                    //TODO
                    .set(vehicleModel2017.vehicleStatus).equalTo(VehicleApplyStatusEnum.MODEL_APPLYING.getStatus())

                    .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                    .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                    .set(vehicleModel2017.updatedTime).equalTo(new Date())
                    .set(vehicleModel2017.modelRegisterTime).equalTo(new Date())
                    .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                    .and(vehicleModel2017.vehicleStatus, isIn(VehicleApplyStatusEnum.MODEL_ENTERING.getStatus(),
                        VehicleApplyStatusEnum.MODEL_ACCESS_DENY.getStatus()))
                    .build().render(RenderingStrategies.MYBATIS3);
        }


        else if (VehicleApplyStatusEnum.MODEL_ACCESS.getStatus().equals(vehicleModelOptional.get().getVehicleStatus())) {
            // 资料退回， 进入对接成功
            updateVehicleStatus = update(vehicleModel2017)
                    .set(vehicleModel2017.vehicleStatus).equalTo(VehicleApplyStatusEnum.MODEL_ACCESS_APPROVE.getStatus())
                    .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                    .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                    .set(vehicleModel2017.updatedTime).equalTo(new Date())
                    .set(vehicleModel2017.modelRegisterTime).equalTo(new Date())
                    .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                    .and(vehicleModel2017.vehicleStatus, isIn(VehicleApplyStatusEnum.MODEL_ACCESS.getStatus()))
                    .build().render(RenderingStrategies.MYBATIS3);
        } else {
            throw new ServiceException("当前状态不可提交");
        }
        vehicleModel2017Mapper.update(updateVehicleStatus);
        return new BaseResponse();
    }


    @Override
    @ModelLog(type = ModelLogTypeEnum.UPLOAD_CERTIFICATE, model = "#modelInfo.modelId", org = "#modelInfo.orgId",
            uId = "#modelInfo.userId", uName = "#modelInfo.userName")
    public BaseResponse uploadCertificate(UploadModelFileDTO modelInfo) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        fileService.clearFile(FileTypeEnum.VEHICLE_MODEL_CERTIFICATE, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.VEHICLE_MODEL_CERTIFICATE, modelInfo.getPath(), modelInfo.getModelId(),
                modelInfo.getUserId(), modelInfo.getUserName());
        UpdateStatementProvider uploadReviewReport = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.dataAccessCertificateCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(uploadReviewReport);
        return new BaseResponse();
    }

    @Override
    public BaseResponse accessApprove(String modelId, String orgId, String userId, String userName) {
        //获取车型信息
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelId);
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        //校验当前状态
        AssertUtil.isTrue(VehicleApplyStatusEnum.MODEL_ACCESS.getStatus().equals(vehicleModel.getVehicleStatus()), "当前状态不可确认");

        //更新数据库状态
        UpdateStatementProvider approve = update(vehicleModel2017)
                .set(vehicleModel2017.modelAccessStatus).equalTo(1)
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.vehicleStatus, isEqualTo(VehicleApplyStatusEnum.MODEL_ACCESS.getStatus()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(approve);
        return new BaseResponse();
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.DATA_ACCESS_DENY, model = "#modelId", org = "#orgId",
            uId = "#userId", uName = "#userName")
    public BaseResponse accessDeny(String modelId, String orgId, String userId, String userName) {
        //获取车型信息
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelId);
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        //校验当前状态
        AssertUtil.isTrue(VehicleApplyStatusEnum.MODEL_ACCESS.getStatus().equals(vehicleModel.getVehicleStatus()), "当前状态不可退回");

        //更新车型状态
        UpdateStatementProvider deny = update(vehicleModel2017)
                .set(vehicleModel2017.vehicleStatus).equalTo(VehicleApplyStatusEnum.MODEL_ACCESS_DENY.getStatus())
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.vehicleStatus, isEqualTo(VehicleApplyStatusEnum.MODEL_ACCESS.getStatus()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(deny);
        return new BaseResponse();
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.UPLOAD_REVIEW_REPORT, model = "#modelInfo.modelId", org = "#modelInfo.orgId",
            uId = "#modelInfo.userId", uName = "#modelInfo.userName")
    public BaseResponse uploadReviewReport(UploadModelFileDTO modelInfo) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        AssertUtil.isTrue(vehicleModel.getVehicleStatus().equals(VehicleApplyStatusEnum.MODEL_ACCESS_APPROVE.getStatus()), "当前状态不可上传评审报告");
        fileService.clearFile(FileTypeEnum.VEHICLE_MODEL_REVIEW_REPORT, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.VEHICLE_MODEL_REVIEW_REPORT, modelInfo.getPath(), modelInfo.getModelId(),
                modelInfo.getUserId(), modelInfo.getUserName());
        UpdateStatementProvider uploadReviewReport = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.reviewReportCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(uploadReviewReport);
        return new BaseResponse();
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.UPLOAD_CHECK_REPORT, model = "#modelInfo.modelId", org = "#modelInfo.orgId",
            uId = "#modelInfo.userId", uName = "#modelInfo.userName")
    public BaseResponse uploadCheckReport(UploadModelFileDTO modelInfo) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        fileService.clearFile(FileTypeEnum.VEHICLE_MODEL_CHECK_REPORT, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.VEHICLE_MODEL_CHECK_REPORT, modelInfo.getPath(), modelInfo.getModelId(),
                modelInfo.getUserId(), modelInfo.getUserName());
        UpdateStatementProvider uploadCheckReport = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.checkReportCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(uploadCheckReport);
        return new BaseResponse();
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.UPLOAD_PARAM_REPORT, model = "#modelInfo.modelId", org = "#modelInfo.orgId",
            uId = "#modelInfo.userId", uName = "#modelInfo.userName")
    public BaseResponse uploadParamReport(UploadModelFileDTO modelInfo) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        AssertUtil.isTrue(vehicleModel.getVehicleStatus().equals(VehicleApplyStatusEnum.MODEL_ACCESS_APPROVE.getStatus()), "当前状态不可上传车型参数表");
        fileService.clearFile(FileTypeEnum.VEHICLE_MODEL_PARAM_REPORT, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.VEHICLE_MODEL_PARAM_REPORT, modelInfo.getPath(), modelInfo.getModelId(),
                modelInfo.getUserId(), modelInfo.getUserName());
        UpdateStatementProvider uploadParamReport = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.paramReportCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(uploadParamReport);
        return new BaseResponse();
    }


    @Override
    @ModelLog(type = ModelLogTypeEnum.DATUM_CONFIRM, model = "#modelInfo.modelId", org = "#modelInfo.orgId",
            uId = "#modelInfo.userId", uName = "#modelInfo.userName")
    public BaseResponse datumConfirm(DatumConfirmDTO modelInfo) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        AssertUtil.isTrue(VehicleApplyStatusEnum.MODEL_ACCESS_APPROVE.getStatus().equals(vehicleModel.getVehicleStatus()), "当前状态不可资料确认");

        if(new Date().after(DateUtil.parse("2024-01-01 00:00:00", DateUtil.DATE_TYPE1))) {
            if (StringUtils.isBlank(vehicleModel.getModelEvaluationReportCopy())) {
                throw new ServiceException(modelInfo.getModelId() + "：未上传车型评估报告");
            }
        }

        UpdateStatementProvider datumConfirm = update(vehicleModel2017)
                .set(vehicleModel2017.vehicleStatus).equalTo(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.modelEvaluationTime).equalTo(new Date())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .and(vehicleModel2017.vehicleStatus, isEqualTo(VehicleApplyStatusEnum.MODEL_ACCESS_APPROVE.getStatus()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(datumConfirm);
        return new BaseResponse();
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.DATUM_DENY, model = "#modelId", org = "#orgId",
            uId = "#userId", uName = "#userName")
    public BaseResponse datumDeny(String modelId, String orgId, String userId, String userName) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelId);
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        AssertUtil.isIn(vehicleModel.getVehicleStatus(), Arrays.asList(VehicleApplyStatusEnum.MODEL_ACCESS_APPROVE.getStatus(), VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus()), "当前状态不可资料退回");
        UpdateStatementProvider datumDeny = update(vehicleModel2017)
                .set(vehicleModel2017.vehicleStatus).equalTo(VehicleApplyStatusEnum.MODEL_ACCESS.getStatus())
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.vehicleStatus, isIn(VehicleApplyStatusEnum.MODEL_ACCESS_APPROVE.getStatus(), VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(datumDeny);
        return new BaseResponse();
    }


    @Override
    @ModelLog(type = ModelLogTypeEnum.REPORT_CONFIRM, model = "#modelId", org = "#orgId",
            uId = "#userId", uName = "#userName")
    public BaseResponse reportConfirm(String modelId, String orgId, String userId, String userName) {
        //获取车型信息
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelId);
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();

        //校验当前状态
        AssertUtil.isTrue(VehicleApplyStatusEnum.MODEL_DATA_CONFIRM.getStatus().equals(vehicleModel.getVehicleStatus()), "当前状态不可评审通过");

        //校验数据中心 是否已经上传评估报告
        //校验检测中心 是否已经上传评估报告
        //TODO 确认评估报告
        return new BaseResponse();
    }


    @Override
    @ModelLog(type = ModelLogTypeEnum.ALLOW_GET_LICENSE, model = "#modelId", org = "#orgId",
            uId = "#userId", uName = "#userName")
    public BaseResponse allowGetLicense(String modelId, String orgId, String userId, String userName) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelId);
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        AssertUtil.isTrue(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus().equals(vehicleModel.getVehicleStatus()), "当前状态不可允许上牌");
        UpdateStatementProvider allowGetLicense = update(vehicleModel2017)
                .set(vehicleModel2017.vehicleStatus).equalTo(VehicleApplyStatusEnum.MODEL_LICENSING_APPROVE.getStatus())
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.vehicleStatus, isEqualTo(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(allowGetLicense);
        return new BaseResponse();
    }


    @Override
    public byte[] downloadVehicleParam(List<String> modelIds, Integer type) {
        List<VehicleModel2017> vehicleModels = vehicleModel2017Mapper.select(model -> model.where(vehicleModel2017.vehicleModelId, isIn(modelIds)));
        return PDFUtilsForVehicleModel.createVehicleModelPDF(vehicleModels, "车型参数.pdf");
    }


    @Override
    public FileInfoBO getFile(GetVehicleModelFileDTO getVehicleModelFileDTO) {
        Optional<VehicleModel2017> vehicleModel2017 = vehicleModel2017Mapper.selectByPrimaryKey(getVehicleModelFileDTO.getModelId());
        AssertUtil.isTrue(vehicleModel2017.isPresent(), "该记录不存在");
        String path = StringUtils.EMPTY;
        FileTypeEnum fileType = null;
        switch (getVehicleModelFileDTO.getType()) {
            case 1:
                fileType = FileTypeEnum.VEHICLE_MODEL_APPLY_DOC;
                path = vehicleModel2017.get().getApplicationCopy();
                break;
            case 2:
                fileType = FileTypeEnum.VEHICLE_MODEL_CONFIRM_DOC;
                path = vehicleModel2017.get().getApplicationDetailCopy();
                break;
            case 3:
                fileType = FileTypeEnum.VEHICLE_MODEL_CERTIFICATE;
                path = vehicleModel2017.get().getDataAccessCertificateCopy();
                break;
            case 4:
                fileType = FileTypeEnum.VEHICLE_MODEL_REVIEW_REPORT;
                path = vehicleModel2017.get().getReviewReportCopy();
                break;
            case 5:
                fileType = FileTypeEnum.VEHICLE_MODEL_PARAM_REPORT;
                path = vehicleModel2017.get().getParamReportCopy();
                break;
            case 6:
                fileType = FileTypeEnum.VEHICLE_MODEL_CHECK_REPORT;
                path = vehicleModel2017.get().getCheckReportCopy();
                break;
            case 7:
                fileType = FileTypeEnum.MODEL_REGISTER_APPLICATION;
                path = vehicleModel2017.get().getModelRegisterApplicationCopy();
                break;
            case 8:
                fileType = FileTypeEnum.PLATFORM_COMPLIANCE_REPORT;
                path = vehicleModel2017.get().getPlatformComplianceReportCopy();
                break;
            case 9:
                fileType = FileTypeEnum.MODEL_EVALUATION_REPORT;
                path = vehicleModel2017.get().getModelEvaluationReportCopy();
                break;
            //数据中心评估报告
            case 10:
                fileType = FileTypeEnum.MODEL_EVALUATION_DATA_REPORT;
                break;
            //检测中心评估报告
            case 11:
                fileType = FileTypeEnum.MODEL_EVALUATION_TESTING_REPORT;
                break;

            default:
        }
        FileInfoBO fileInfoBO = new FileInfoBO();
        if (fileType != null)  {
            List<FileInfoDTO> fileInfo = fileService.getFileInfo(fileType, getVehicleModelFileDTO.getModelId());
            fileInfoBO.setFileInfo(fileInfo);
        }
        return fileInfoBO;
    }

    @Override
    public PageInfoBO<VehicleModelOperateLog> getModelOperateLog(String modelId) {
        List<VehicleModelOperateLog> operateLogs = vehicleModelOperateLogMapper.select(c ->
                c.where(vehicleModelOperateLog.vehicleModelId, isEqualTo(modelId)).orderBy(vehicleModelOperateLog.id.descending()));
        PageInfoBO<VehicleModelOperateLog> pageInfoBO = new PageInfoBO<>();
        pageInfoBO.setRows(operateLogs);
        return pageInfoBO;
    }

    @Override
    public byte[] exportVehicleModel(SearchVehicleModelApplyDTO searchVehicleModelApplyDTO) {
        SelectStatementProvider searchVehicleModel = select(vehicleModel2017.vehicleModelId, vehicleModel2017.vehicleModelName,
                vehicleModel2017.vehicleType, vehicleModel2017.orgProductName, vehicleModel2017.vehicleStatus, vehicleModel2017.isMakeup)
                .from(vehicleModel2017)
                .where(vehicleModel2017.vehicleModelId, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleModelId()))
                .and(vehicleModel2017.vehicleType, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleType()))
                .and(vehicleModel2017.vehicleStatus, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleStatus()))
                .and(vehicleModel2017.orgProductId, isEqualToWhenPresent(searchVehicleModelApplyDTO.getVehicleOrgId()))
                .and(vehicleModel2017.vehicleStatus, isIn(searchVehicleModelApplyDTO.getBoundStatus()))
                .limit(searchVehicleModelApplyDTO.getLimit()).offset(searchVehicleModelApplyDTO.getOffset())
                .build().render(RenderingStrategies.MYBATIS3);
        List<VehicleModel2017> vehicleModel2017List = vehicleModel2017Mapper.selectMany(searchVehicleModel);
        ArrayList<ExportVehicleModelDTO> exportVehicleModelDTOS = new ArrayList<>();
        for (VehicleModel2017 model2017 : vehicleModel2017List) {
            ExportVehicleModelDTO exportVehicleModelDTO = new ExportVehicleModelDTO();
            exportVehicleModelDTO.setVehicleModelId(model2017.getVehicleModelId());
            exportVehicleModelDTO.setVehicleModelName(model2017.getVehicleModelName());
            exportVehicleModelDTO.setOrgProductName(model2017.getOrgProductName());
            exportVehicleModelDTO.setVehicleType(TransUtil.transVehicleType(model2017.getVehicleType()));
            exportVehicleModelDTO.setVehicleStatus(TransUtil.transVehicleModelStatus(model2017.getVehicleStatus()));
            exportVehicleModelDTO.setIsMakeup("1".equals(model2017.getIsMakeup()) ? "是" : "否");
            exportVehicleModelDTOS.add(exportVehicleModelDTO);
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, ExportVehicleModelDTO.class).withTemplate(ExcelUtil.getVehicleModelExcel()).sheet().doFill(exportVehicleModelDTOS);
        return outputStream.toByteArray();
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.DATUM_ROLLBACK, model = "#modelId", org = "#orgId", uId = "#userId", uName = "#userName")
    public BaseResponse datumRollback(String modelId, String orgId, String userId, String userName) {
        //校验车型是否存在
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelId);
        if(!vehicleModelOptional.isPresent()){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验当前状态能否退回
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        if(!VehicleApplyStatusEnum.MODEL_DATA_CONFIRM.getStatus().equals(vehicleModel.getVehicleStatus())){
            throw new ServiceException("当前状态无法退回");
        }

        //校验通过，更新数据库状态
        updateVehicleModelStatus(modelId,VehicleApplyStatusEnum.MODEL_ACCESS_APPROVE, userId, userName, VehicleApplyStatusEnum.MODEL_DATA_CONFIRM.getStatus());
        return new BaseResponse();
    }

    @Override
    public void uploadModelRegisterApplication(UploadModelFileDTO modelInfo) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        if (!canUpdateModelByVehicleOrg(vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不可上传车型登记申请表");
        }
        fileService.clearFile(FileTypeEnum.MODEL_REGISTER_APPLICATION, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.MODEL_REGISTER_APPLICATION, modelInfo.getPath(), modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        UpdateStatementProvider uploadReviewReport = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.modelRegisterApplicationCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(uploadReviewReport);
    }

    @Override
    public void uploadPlatformComplianceReport(UploadModelFileDTO modelInfo) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        if (!canUpdateModelByVehicleOrg(vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不可上传平台符合性报告");
        }
        //更新文件实际位置
        fileService.clearFile(FileTypeEnum.PLATFORM_COMPLIANCE_REPORT, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.PLATFORM_COMPLIANCE_REPORT, modelInfo.getPath(), modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());

        //更新标识字段，以表示该文件已上传，可以查看详情
        UpdateStatementProvider uploadReviewReport = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.platformComplianceReportCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(uploadReviewReport);
    }

    @Override
    public void uploadModelEvaluationReport(UploadModelFileDTO modelInfo) {
        Optional<VehicleModel2017> vehicleModelOptional = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId());
        AssertUtil.isTrue(vehicleModelOptional.isPresent(), "该记录不存在，请刷新后重试");
        VehicleModel2017 vehicleModel = vehicleModelOptional.get();
        AssertUtil.isTrue(vehicleModel.getVehicleStatus().equals(VehicleApplyStatusEnum.MODEL_ACCESS_APPROVE.getStatus()), "当前状态不可上传车型评估报告");
        fileService.clearFile(FileTypeEnum.MODEL_EVALUATION_REPORT, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.MODEL_EVALUATION_REPORT, modelInfo.getPath(), modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        UpdateStatementProvider uploadReviewReport = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.modelEvaluationReportCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(uploadReviewReport);
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.ACCEPT_MODEL, model = "#modelId", org = "#orgId", uId = "#userId", uName = "#userName")
    public void accept(String modelId, String orgId, String userId, String userName) {
        //获取车型信息id
        VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(modelId).orElse(null);
        if(vehicleModel == null){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验状态
        VehicleApplyStatusEnum destVehicleStatus;
        //如果是申请中，则更新为 车型核实中
        if(Objects.equals(VehicleApplyStatusEnum.MODEL_APPLYING.getStatus(), vehicleModel.getVehicleStatus())){
            destVehicleStatus =  VehicleApplyStatusEnum.MODEL_ACCESS;
        }
        //如果是车型核实中，且已经确认核实信息，则更新为评估中
        else if (Objects.equals(VehicleApplyStatusEnum.MODEL_ACCESS.getStatus(), vehicleModel.getVehicleStatus())
            && vehicleModel.getModelAccessStatus() == 1){
            destVehicleStatus =  VehicleApplyStatusEnum.MODEL_EVALUATION;
        }else{
            //只有申请中、车型核实确认的数据才能更新
            throw new ServiceException("当前状态不可受理，请刷新后重试");
        }

        //更新状态
        updateVehicleModelStatus(modelId, destVehicleStatus, userId, userName, VehicleApplyStatusEnum.MODEL_APPLYING.getStatus(), VehicleApplyStatusEnum.MODEL_ACCESS.getStatus());
    }

    @Override
    public void uploadDataCenterEvaluationReport(UploadModelFileDTO modelInfo) {
        //获取车型信息
        VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId()).orElse(null);
        if(vehicleModel == null){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验当前车型状态 - 只有评估中的车型能上传拿报告
        if(!Objects.equals(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus(), vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不可提交，请刷新后重试");
        }
        //校验报告状态，只有被退回或未提交的报告，能够上传覆盖
        //0:未提交 1:已提交 2:打回
        if(vehicleModel.getModelEvaluationDataStatus() == 1){
            throw new ServiceException("您已提交该报告，无法重新提交");
        }

        //更新文件实际位置
        fileService.clearFile(FileTypeEnum.MODEL_EVALUATION_DATA_REPORT, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.MODEL_EVALUATION_DATA_REPORT, modelInfo.getPath(), modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());

        //更新标识字段，以表示该文件已上传，可以查看详情
        UpdateStatementProvider updateStatement = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.modelEvaluationDataReportCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(updateStatement);
    }

    @Override
    public void uploadTestingCenterEvaluationReport(UploadModelFileDTO modelInfo) {
        //获取车型信息
        VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(modelInfo.getModelId()).orElse(null);
        if(vehicleModel == null){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验当前车型状态 - 只有评估中的车型能上传拿报告
        if(!Objects.equals(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus(), vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不可提交，请刷新后重试");
        }

        //校验报告状态，只有被退回或未提交的报告，能够上传覆盖
        //0:未提交 1:已提交 2:打回
        if(vehicleModel.getModelEvaluationTestingStatus() == 1){
            throw new ServiceException("您已提交该报告，无法重新提交");
        }

        //更新文件实际位置
        fileService.clearFile(FileTypeEnum.MODEL_EVALUATION_TESTING_REPORT, modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());
        fileService.saveFileInfo(FileTypeEnum.MODEL_EVALUATION_TESTING_REPORT, modelInfo.getPath(), modelInfo.getModelId(), modelInfo.getUserId(), modelInfo.getUserName());

        //更新标识字段，以表示该文件已上传，可以查看详情
        UpdateStatementProvider updateStatement = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.modelEvaluationTestingReportCopy).equalTo("MOVE_TO_UPLOAD_FILE_INFO")
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(modelInfo.getUserId())
                .set(vehicleModel2017.updatedUserName).equalTo(modelInfo.getUserName())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelInfo.getModelId()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(updateStatement);
    }

    @Override
    public void submitDataCenterEvaluation(String modelId, String orgId, String userId, String userName) {
        //获取车型信息
        VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(modelId).orElse(null);
        if(vehicleModel == null){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验当前车型状态，只有评估中的车型能提交
        if(!Objects.equals(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus(), vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不可提交，请刷新后重试");
        }
        //校验报告状态，只有被退回或未提交的车型能够提交
        //0:未提交 1:已提交 2:打回
        if(vehicleModel.getModelEvaluationDataStatus() == 1){
            throw new ServiceException("您已提交申请，无法重新提交");
        }
        //校验是否已经上传报告
        if(StringUtils.isBlank(vehicleModel.getModelEvaluationDataReportCopy())){
            throw new ServiceException("请先上传评估报告");
        }

        UpdateStatementProvider updateStatement = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.modelEvaluationDataStatus).equalTo(1)
                .set(vehicleModel2017.modelEvaluationDataSubmitTime).equalTo(new Date())
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.modelEvaluationDataStatus, isIn(0, 2))
                .and(vehicleModel2017.vehicleStatus, isEqualTo(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(updateStatement);
    }

    @Override
    public void submitTestingCenterEvaluation(String modelId, String orgId, String userId, String userName) {
        //获取车型信息
        VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(modelId).orElse(null);
        if(vehicleModel == null){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验当前车型状态，只有评估中的车型能提交
        if(!Objects.equals(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus(), vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不可提交，请刷新后重试");
        }
        //校验报告状态，只有被退回或未提交的车型能够提交
        //0:未提交 1:已提交 2:打回
        if(vehicleModel.getModelEvaluationTestingStatus() == 1){
            throw new ServiceException("您已提交申请，无法重新提交");
        }
        //校验是否已经上传报告
        if(StringUtils.isBlank(vehicleModel.getModelEvaluationTestingReportCopy())){
            throw new ServiceException("请先上传评估报告");
        }

        UpdateStatementProvider updateStatement = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.modelEvaluationTestingStatus).equalTo(1)
                .set(vehicleModel2017.modelEvaluationTestingSubmitTime).equalTo(new Date())
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.modelEvaluationTestingStatus, isIn(0, 2))
                .and(vehicleModel2017.vehicleStatus, isEqualTo(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(updateStatement);
    }

    @Override
    public void dataCenterEvaluationDeny(String modelId, String orgId, String userId, String userName) {

        //获取车型信息
        VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(modelId).orElse(null);
        if(vehicleModel == null){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验当前车型状态
        if(!Objects.equals(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus(), vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不可退回，请刷新后重试");
        }

        //只有确认中的报告可以退回
        //0:未提交 1:已提交 2:打回
        if(vehicleModel.getModelEvaluationDataStatus() != 1){
            throw new ServiceException("当前状态不可退回，请刷新后重试");
        }

        //更新状态
        UpdateStatementProvider updateStatement = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.modelEvaluationDataStatus).equalTo(2)
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.modelEvaluationDataStatus, isEqualTo(1))
                .and(vehicleModel2017.vehicleStatus, isEqualTo(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(updateStatement);
    }

    @Override
    public void testingCenterEvaluationDeny(String modelId, String orgId, String userId, String userName) {
        //获取车型信息
        VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(modelId).orElse(null);
        if(vehicleModel == null){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验当前车型状态
        if(!Objects.equals(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus(), vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不可退回，请刷新后重试");
        }
        //只有确认中的报告可以退回
        if(vehicleModel.getModelEvaluationTestingStatus() != 1){
            throw new ServiceException("当前状态不可退回，请刷新后重试");
        }

        //更新状态
        UpdateStatementProvider updateStatement = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.modelEvaluationTestingStatus).equalTo(2)
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.modelEvaluationTestingStatus, isEqualTo(1))
                .and(vehicleModel2017.vehicleStatus, isEqualTo(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(updateStatement);
    }

    @Override
    @ModelLog(type = ModelLogTypeEnum.EVALUATION_APPROVE, model = "#modelId", org = "#orgId",
            uId = "#userId", uName = "#userName")
    public void evaluationApprove(String modelId, String orgId, String userId, String userName) {
        //获取车型信息
        VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(modelId).orElse(null);
        if(vehicleModel == null){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验当前车型状态
        if(!Objects.equals(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus(), vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不可确认，请刷新后重试");
        }
        if(vehicleModel.getModelEvaluationStatus() != 0){
            throw new ServiceException("当前状态不可确认，请刷新后重试");
        }

        if(StringUtils.isBlank(vehicleModel.getModelEvaluationDataReportCopy())){
            throw new ServiceException("数据中心评估报告缺失");
        }

        if(vehicleModel.getModelEvaluationDataStatus() != 1){
            throw new ServiceException("数据中心评估报告未提交");
        }

        if(StringUtils.isBlank(vehicleModel.getModelEvaluationTestingReportCopy())){
            throw new ServiceException("检测中心评估报告缺失");
        }
        if(vehicleModel.getModelEvaluationTestingStatus() != 1){
            throw new ServiceException("检测中心评估报告未提交");
        }

        UpdateStatementProvider updateStatement = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.modelEvaluationStatus).equalTo(1)
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.modelEvaluationStatus, isEqualTo(0))
                .and(vehicleModel2017.vehicleStatus, isEqualTo(VehicleApplyStatusEnum.MODEL_EVALUATION.getStatus()))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(updateStatement);
    }

    @Override
    public FileResponse downloadVehicleModelFiles(List<String> ids) {
        if(ids == null || ids.isEmpty()){
            throw new ServiceException("请选择需要下载文件的车型");
        }
        List<Integer> fileTypeList = Arrays.asList(
                FileTypeEnum.MODEL_REGISTER_APPLICATION.getFileType(),
                FileTypeEnum.MODEL_EVALUATION_DATA_REPORT.getFileType(),
                FileTypeEnum.MODEL_EVALUATION_TESTING_REPORT.getFileType());
        List<UploadFileInfo> modelFileInfoList = fileService.getModelFileInfo(fileTypeList, ids);


        String uuid = UUID.randomUUID().toString();

        //临时缓存车型报告目录
        String tmpFileRoot = Const.TMP_FILE_PATH.concat(File.separator)
                .concat("modelFileTmp").concat(File.separator)
                .concat(uuid).concat(File.separator);

        //OSS目录
        String ossTmpZipFileName =  "modelFileTmp".concat(File.separator)
                .concat(uuid + ".zip").replace("\\","/");


        //如果pathName不存在，则创建
        File directory = new File(tmpFileRoot);
        if (!directory.exists()) {
            directory.mkdirs();
        }

        //将报告文件下载至本地临时文件夹
        for (UploadFileInfo uploadFileInfo : modelFileInfoList) {
            //查询车型信息
            VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(uploadFileInfo.getRelationIdStr()).orElse(null);
            if(vehicleModel != null){
                //逐个下载文件到本地
                String objectName = Const.OSS_FILE_HEAD  +  uploadFileInfo.getFileCopyOss();
                //根据原文件地址，获取文件拓展名
                String fileExtension = StringUtils.substringAfterLast(uploadFileInfo.getFileCopy(), Const.DOT);
                //获取文件类型
                FileTypeEnum fileTypeEnum = FileTypeEnum.parse(uploadFileInfo.getFileType());
                String pathName = tmpFileRoot+ vehicleModel.getVehicleModelId() + "-" + fileTypeEnum.getFileTypeName() + "." + fileExtension;
                OSSFileUtils.download(objectName, pathName );
            }
        }

        //将文件夹进行压缩添加至压缩包
        try{
            byte[] zipFileByte = FileUtil.zip(tmpFileRoot);
            //将文件上传到OSS
            InputStream zipFileInputStream = new ByteArrayInputStream(zipFileByte);
            //文件路径
            OSSFileUtils.upload(Const.OSS_FILE_HEAD + ossTmpZipFileName, zipFileInputStream);
        }catch (Exception e){
            log.error("压缩车型报告失败" + e.getLocalizedMessage(), e);
            throw new ServiceException("压缩车型报告失败");
        }finally {
            //删除临时文件
            FileUtil.deleteDirectory(directory);
        }
        return new FileResponse(ossTmpZipFileName, Const.OSS_FILE_ABSOLUTE_PATH.concat(ossTmpZipFileName));
    }

    @Override
    public void accessReturn(String modelId, String orgId, String userId, String userName) {
        //获取车型信息
        VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(modelId).orElse(null);
        if(vehicleModel == null){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验当前车型状态
        if(!Objects.equals(VehicleApplyStatusEnum.MODEL_ACCESS.getStatus(), vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前状态不可退回，请刷新后重试");
        }
        //只有确认中的报告可以退回
        if(vehicleModel.getModelAccessStatus() != 1){
            throw new ServiceException("当前状态不可退回，请刷新后重试");
        }

        //更新状态
        UpdateStatementProvider updateStatement = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.modelAccessStatus).equalTo(2)
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.modelAccessStatus, isEqualTo(1))
                .and(vehicleModel2017.vehicleStatus, isEqualTo(VehicleApplyStatusEnum.MODEL_ACCESS.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(updateStatement);
    }

    @Override
    public void terminate(String modelId, String remark,String userId,  String userName) {
        //获取车型信息
        VehicleModel2017 vehicleModel = vehicleModel2017Mapper.selectByPrimaryKey(modelId).orElse(null);
        if(vehicleModel == null){
            throw new ServiceException("该记录不存在，请刷新后重试");
        }

        //校验当前车型状态
        if(Objects.equals(VehicleApplyStatusEnum.MODEL_LICENSING_APPROVE.getStatus(), vehicleModel.getVehicleStatus())) {
            throw new ServiceException("当前车型已经完成审核流程，无法终止，请刷新后重试");
        }

        if(StringUtils.isBlank(remark)){
            throw new ServiceException("请填写终止原因");
        }

        //更新状态
        UpdateStatementProvider updateStatement = SqlBuilder.update(vehicleModel2017)
                .set(vehicleModel2017.vehicleStatus).equalTo(VehicleApplyStatusEnum.MODEL_TERMINAL.getStatus())
                .set(vehicleModel2017.remarkEitc).equalTo(remark)
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .set(vehicleModel2017.updatedUser).equalTo(userId)
                .set(vehicleModel2017.updatedUserName).equalTo(userName)
                .where(vehicleModel2017.vehicleModelId, isEqualTo(modelId))
                .and(vehicleModel2017.vehicleStatus, isNotEqualTo(VehicleApplyStatusEnum.MODEL_LICENSING_APPROVE.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(updateStatement);
    }

    private Boolean canUpdateModelByVehicleOrg(Integer modelStatus) {
        //只有录入 和 退回状态 车企才能上传材料
        return VehicleApplyStatusEnum.MODEL_ENTERING.getStatus().equals(modelStatus)
                || VehicleApplyStatusEnum.MODEL_ACCESS_DENY.getStatus().equals(modelStatus);
    }

    private Boolean canUpdateModelByDataCenter() {
        return false;
    }

    private Boolean canUpdateModelByTestingCenter() {
        return false;
    }


    /**
     * 更新车型状态
     * @param vehicleModelId 需要更新的车型ID
     * @param expectStatus 期望更新后的状态
     * @param operatorId 操作人ID
     * @param operatorName 操作人名称
     * @param restrictStatus 限制的状态
     */
    private void updateVehicleModelStatus(String vehicleModelId, VehicleApplyStatusEnum expectStatus, String operatorId, String operatorName, Integer... restrictStatus){
        UpdateStatementProvider updateStatementProvider = update(vehicleModel2017)
                .set(vehicleModel2017.vehicleStatus).equalTo(expectStatus.getStatus())
                .set(vehicleModel2017.updatedUser).equalTo(operatorId)
                .set(vehicleModel2017.updatedUserName).equalTo(operatorName)
                .set(vehicleModel2017.updatedTime).equalTo(new Date())
                .where(vehicleModel2017.vehicleModelId, isEqualTo(vehicleModelId))
                .and(vehicleModel2017.vehicleStatus, isInWhenPresent(restrictStatus))
                .build().render(RenderingStrategies.MYBATIS3);
        vehicleModel2017Mapper.update(updateStatementProvider);
    }


}
